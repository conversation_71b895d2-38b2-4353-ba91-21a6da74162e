const settings = Object.assign({}, userSettings);

const AdminSettings = {
  // Settings INIT
  AdminSettingsInit() {
    this.ManageThemeLayout();
    this.ManageSidebarType();
    this.ManageBoxedLayout();
    this.ManageDirectionLayout();
    this.ManageDarkThemeLayout();
    this.ManageColorThemeLayout();
    this.ManageCardLayout();
  },

  // Vertical / Horizontal Layout
  ManageThemeLayout() {
    const horizontalLayoutElement =
      document.getElementById("horizontal-layout");
    const verticalLayoutElement = document.getElementById("vertical-layout");

    switch (settings.Layout) {
      case "horizontal":
        if (horizontalLayoutElement) {
          horizontalLayoutElement.checked = true;
        }
        document.documentElement.setAttribute("data-layout", "horizontal");
        break;
      case "vertical":
        if (verticalLayoutElement) {
          verticalLayoutElement.checked = true;
        }
        document.documentElement.setAttribute("data-layout", "vertical");
        break;
      default:
        break;
    }
  },

  // Full / Mini Sidebar Type
  ManageSidebarType() {
    switch (settings.SidebarType) {
      case "full":
        const fullSidebarElement = document.querySelector("#full-sidebar");
        if (fullSidebarElement) {
          fullSidebarElement.checked = true;
        }
        document.body.setAttribute("data-sidebartype", "full");

        const setSidebarType = () => {
          const width =
            window.innerWidth > 0 ? window.innerWidth : screen.width;
          if (width < 1300) {
            document.body.setAttribute("data-sidebartype", "mini-sidebar");
          } else {
            document.body.setAttribute("data-sidebartype", "full");
          }
        };
        window.addEventListener("DOMContentLoaded", setSidebarType);
        window.addEventListener("resize", setSidebarType);
        break;

      case "mini-sidebar":
        const miniSidebarElement = document.querySelector("#mini-sidebar");
        if (miniSidebarElement) {
          miniSidebarElement.checked = true;
        }
        document.body.setAttribute("data-sidebartype", "mini-sidebar");
        break;

      default:
        break;
    }
  },

  // Layout Boxed or Full
  ManageBoxedLayout() {
    const boxedLayoutElement = document.getElementById("boxed-layout");
    const fullLayoutElement = document.getElementById("full-layout");

    if (boxedLayoutElement) boxedLayoutElement.checked = true;
    switch (settings.BoxedLayout) {
      case true:
        document.documentElement.setAttribute("data-boxed-layout", "boxed");
        if (boxedLayoutElement) boxedLayoutElement.checked = true;
        break;
      case false:
        document.documentElement.setAttribute("data-boxed-layout", "full");
        if (fullLayoutElement) fullLayoutElement.checked = true;
        break;
      default:
        break;
    }
  },

  // Direction Type
  ManageDirectionLayout() {
    const ltrLayoutElement = document.getElementById("ltr-layout");
    const rtlLayoutElement = document.getElementById("rtl-layout");

    switch (settings.Direction) {
      case "ltr":
        if (ltrLayoutElement) {
          ltrLayoutElement.checked = true;
        }
        document.documentElement.setAttribute("dir", "ltr");
        const offcanvasStart = document.querySelector(".offcanvas-start");
        if (offcanvasStart) {
          offcanvasStart.classList.toggle("offcanvas-end");
          offcanvasStart.classList.remove("offcanvas-start");
        }
        break;
      case "rtl":
        document.documentElement.setAttribute("dir", "rtl");
        const offcanvasEnd = document.querySelector(".offcanvas-end");
        if (offcanvasEnd) {
          offcanvasEnd.classList.toggle("offcanvas-start");
          offcanvasEnd.classList.remove("offcanvas-end");
        }
        if (rtlLayoutElement) {
          rtlLayoutElement.checked = true;
        }
        break;
      default:
        break;
    }
  },

  // Card Type
  ManageCardLayout() {
    const cardWithoutBorderElement = document.getElementById(
      "card-without-border"
    );
    const cardWithBorderElement = document.getElementById("card-with-border");

    if (cardWithoutBorderElement) cardWithoutBorderElement.checked = true;
    switch (settings.cardBorder) {
      case true:
        document.documentElement.setAttribute("data-card", "border");
        if (cardWithBorderElement) cardWithBorderElement.checked = true;
        break;
      case false:
        document.documentElement.setAttribute("data-card", "shadow");
        if (cardWithoutBorderElement) cardWithoutBorderElement.checked = true;
        break;
      default:
        break;
    }
  },

  // Theme Dark or Light
  ManageDarkThemeLayout() {
    const setTheme = (theme, hideElements, showElements, hideElements2) => {
      document.documentElement.setAttribute("data-bs-theme", theme);
      const themeLayoutElement = document.getElementById(`${theme}-layout`);
      if (themeLayoutElement) {
        themeLayoutElement.checked = true;
      }

      hideElements.forEach((el) =>
        document
          .querySelectorAll(`.${el}`)
          .forEach((e) => (e.style.display = "none"))
      );
      showElements.forEach((el) =>
        document
          .querySelectorAll(`.${el}`)
          .forEach((e) => (e.style.display = "flex"))
      );
      hideElements2.forEach((el) =>
        document
          .querySelectorAll(`.${el}`)
          .forEach((e) => (e.style.display = "none"))
      );
    };

    switch (settings.Theme) {
      case "light":
        setTheme("light", ["light-logo"], ["moon"], ["sun"]);
        break;
      case "dark":
        setTheme("dark", ["dark-logo"], ["sun"], ["moon"]);
        break;
      default:
        break;
    }
  },

  // Theme Color
  ManageColorThemeLayout() {
    const { ColorTheme } = settings;
    const colorThemeElement = document.getElementById(ColorTheme);

    if (colorThemeElement) {
      document.documentElement.setAttribute("data-color-theme", ColorTheme);
      colorThemeElement.checked = true;
    }
  },
};

// Initialize settings
AdminSettings.AdminSettingsInit();

// Handle Click
document.addEventListener("DOMContentLoaded", function () {
  // Theme Direction RTL LTR click
  function handleDirection() {
    const rtlLayoutElement = document.getElementById("rtl-layout");
    const ltrLayoutElement = document.getElementById("ltr-layout");

    if (rtlLayoutElement) {
      rtlLayoutElement.addEventListener("click", function () {
        document.documentElement.setAttribute("dir", "rtl");
        const offcanvasEnd = document.querySelector(".offcanvas-end");
        if (offcanvasEnd) {
          offcanvasEnd.classList.toggle("offcanvas-start");
          offcanvasEnd.classList.remove("offcanvas-end");
        }
      });
    }

    if (ltrLayoutElement) {
      ltrLayoutElement.addEventListener("click", function () {
        document.documentElement.setAttribute("dir", "ltr");
        const offcanvasStart = document.querySelector(".offcanvas-start");
        if (offcanvasStart) {
          offcanvasStart.classList.toggle("offcanvas-end");
          offcanvasStart.classList.remove("offcanvas-start");
        }
      });
    }
  }

  handleDirection();

  // Theme Layout Box or Full
  function handleBoxedLayout() {
    const boxedLayout = document.getElementById("boxed-layout");
    const fullLayout = document.getElementById("full-layout");
    const containerFluid = document.querySelectorAll(".container-fluid");

    if (boxedLayout) {
      boxedLayout.addEventListener("click", function () {
        containerFluid.forEach((element) => element.classList.remove("mw-100"));
        document.documentElement.setAttribute("data-boxed-layout", "boxed");
        this.checked;
      });
    }

    if (fullLayout) {
      fullLayout.addEventListener("click", function () {
        containerFluid.forEach((element) => element.classList.add("mw-100"));
        document.documentElement.setAttribute("data-boxed-layout", "full");
        this.checked;
      });
    }
  }
  handleBoxedLayout();

  // Theme Layout Vertical or Horizontal
  function handleLayout() {
    const verticalLayout = document.getElementById("vertical-layout");
    const horizontalLayout = document.getElementById("horizontal-layout");

    if (verticalLayout) {
      verticalLayout.addEventListener("click", function () {
        document.documentElement.setAttribute("data-layout", "vertical");
        this.checked;
      });
    }

    if (horizontalLayout) {
      horizontalLayout.addEventListener("click", function () {
        document.documentElement.setAttribute("data-layout", "horizontal");
        this.checked;
      });
    }
  }
  handleLayout();

  // Theme mode Dark or Light
  function handleTheme() {
    function setThemeAttributes(
      theme,
      darkDisplay,
      lightDisplay,
      sunDisplay,
      moonDisplay
    ) {
      document.documentElement.setAttribute("data-bs-theme", theme);
      const themeLayoutElement = document.getElementById(`${theme}-layout`);
      if (themeLayoutElement) {
        themeLayoutElement.checked = true;
      }

      document
        .querySelectorAll(`.${darkDisplay}`)
        .forEach((el) => (el.style.display = "none"));
      document
        .querySelectorAll(`.${lightDisplay}`)
        .forEach((el) => (el.style.display = "flex"));
      document
        .querySelectorAll(`.${sunDisplay}`)
        .forEach((el) => (el.style.display = "none"));
      document
        .querySelectorAll(`.${moonDisplay}`)
        .forEach((el) => (el.style.display = "flex"));
    }

    document.querySelectorAll(".dark-layout").forEach((element) => {
      element.addEventListener("click", () =>
        setThemeAttributes("dark", "dark-logo", "light-logo", "moon", "sun")
      );
    });

    document.querySelectorAll(".light-layout").forEach((element) => {
      element.addEventListener("click", () =>
        setThemeAttributes("light", "light-logo", "dark-logo", "sun", "moon")
      );
    });
  }
  handleTheme();

  // Theme card with Border or Shadow
  function handleCardLayout() {
    function setCardAttribute(cardType) {
      document.documentElement.setAttribute("data-card", cardType);
    }

    const cardWithBorderElement = document.getElementById("card-with-border");
    const cardWithoutBorderElement = document.getElementById(
      "card-without-border"
    );

    if (cardWithBorderElement) {
      cardWithBorderElement.addEventListener("click", () =>
        setCardAttribute("border")
      );
    }
    if (cardWithoutBorderElement) {
      cardWithoutBorderElement.addEventListener("click", () =>
        setCardAttribute("shadow")
      );
    }
  }
  handleCardLayout();

  // Theme Sidebar
  function handleSidebarToggle() {
    function setSidebarType(sidebarType) {
      document.body.setAttribute("data-sidebartype", sidebarType);
    }

    const fullSidebarElement = document.getElementById("full-sidebar");
    const miniSidebarElement = document.getElementById("mini-sidebar");

    if (fullSidebarElement) {
      fullSidebarElement.addEventListener("click", () =>
        setSidebarType("full")
      );
    }
    if (miniSidebarElement) {
      miniSidebarElement.addEventListener("click", () =>
        setSidebarType("mini-sidebar")
      );
    }
  }
  handleSidebarToggle();

  // Toggle Sidebar
  function handleSidebar() {
    document.querySelectorAll(".sidebartoggler").forEach((element) => {
      element.addEventListener("click", function () {
        document.querySelectorAll(".sidebartoggler").forEach((el) => {
          el.checked = true;
        });
        document
          .getElementById("main-wrapper")
          .classList.toggle("show-sidebar");
        document.querySelectorAll(".sidebarmenu").forEach((el) => {
          el.classList.toggle("close");
        });
        const dataTheme = document.body.getAttribute("data-sidebartype");
        if (dataTheme === "full") {
          document.body.setAttribute("data-sidebartype", "mini-sidebar");
        } else {
          document.body.setAttribute("data-sidebartype", "full");
        }
      });
    });
  }
  handleSidebar();
});

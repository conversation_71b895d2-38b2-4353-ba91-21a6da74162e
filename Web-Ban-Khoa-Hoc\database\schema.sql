-- <PERSON><PERSON> Sở D<PERSON> Liệu Web Bán <PERSON>hóa Học
-- Tạo Database
CREATE DATABASE IF NOT EXISTS web_ban_khoa_hoc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE web_ban_khoa_hoc;

-- Bảng Danh <PERSON>ọc
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ten_danh_muc VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    mo_ta TEXT,
    hinh_anh VARCHAR(255),
    thu_tu INT DEFAULT 0,
    trang_thai ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Bảng Người Dùng
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ho_ten VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    mat_khau VARCHAR(255) NOT NULL,
    so_dien_thoai VARCHAR(20),
    dia_chi TEXT,
    avatar VARCHAR(255),
    vai_tro ENUM('admin', 'instructor', 'student') DEFAULT 'student',
    trang_thai ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    email_verified_at TIMESTAMP NULL,
    remember_token VARCHAR(100),
    reset_token VARCHAR(100),
    reset_token_expires TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Bảng Khóa Học
CREATE TABLE courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ten_khoa_hoc VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    mo_ta_ngan TEXT,
    mo_ta_chi_tiet LONGTEXT,
    hinh_anh VARCHAR(255),
    video_gioi_thieu VARCHAR(255),
    gia_goc DECIMAL(10,2) NOT NULL,
    gia_khuyen_mai DECIMAL(10,2),
    thoi_gian_hoc INT, -- Số giờ học
    so_bai_hoc INT DEFAULT 0,
    so_hoc_vien INT DEFAULT 0,
    danh_gia_trung_binh DECIMAL(3,2) DEFAULT 0,
    so_luot_danh_gia INT DEFAULT 0,
    category_id INT,
    instructor_id INT,
    cap_do ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    trang_thai ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    noi_bat BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Bảng Chương Học
CREATE TABLE course_chapters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    ten_chuong VARCHAR(255) NOT NULL,
    mo_ta TEXT,
    thu_tu INT DEFAULT 0,
    trang_thai ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
);

-- Bảng Bài Học
CREATE TABLE course_lessons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chapter_id INT NOT NULL,
    course_id INT NOT NULL,
    ten_bai_hoc VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    noi_dung LONGTEXT,
    video_url VARCHAR(500),
    thoi_luong INT, -- Thời lượng video (giây)
    tai_lieu VARCHAR(255), -- File đính kèm
    thu_tu INT DEFAULT 0,
    mien_phi BOOLEAN DEFAULT FALSE,
    trang_thai ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (chapter_id) REFERENCES course_chapters(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
);

-- Bảng Đăng Ký Khóa Học
CREATE TABLE course_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    ngay_dang_ky TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tien_do DECIMAL(5,2) DEFAULT 0, -- Phần trăm hoàn thành
    trang_thai ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    ngay_hoan_thanh TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_enrollment (user_id, course_id)
);

-- Bảng Tiến Độ Học Tập
CREATE TABLE lesson_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    lesson_id INT NOT NULL,
    course_id INT NOT NULL,
    da_hoan_thanh BOOLEAN DEFAULT FALSE,
    thoi_gian_xem INT DEFAULT 0, -- Thời gian đã xem (giây)
    lan_cuoi_xem TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES course_lessons(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_progress (user_id, lesson_id)
);

-- Bảng Đánh Giá Khóa Học
CREATE TABLE course_reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    diem_danh_gia INT NOT NULL CHECK (diem_danh_gia >= 1 AND diem_danh_gia <= 5),
    noi_dung_danh_gia TEXT,
    trang_thai ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_review (user_id, course_id)
);

-- Bảng Đơn Hàng
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ma_don_hang VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    tong_tien DECIMAL(10,2) NOT NULL,
    trang_thai ENUM('pending', 'completed', 'cancelled', 'refunded') DEFAULT 'pending',
    phuong_thuc_thanh_toan ENUM('bank_transfer', 'momo', 'zalopay', 'vnpay') NOT NULL,
    ghi_chu TEXT,
    ngay_thanh_toan TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Bảng Chi Tiết Đơn Hàng
CREATE TABLE order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    course_id INT NOT NULL,
    ten_khoa_hoc VARCHAR(255) NOT NULL,
    gia DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
);

-- Bảng Giỏ Hàng
CREATE TABLE cart_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_cart_item (user_id, course_id)
);

-- Bảng Coupons/Mã Giảm Giá
CREATE TABLE coupons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ma_coupon VARCHAR(50) UNIQUE NOT NULL,
    ten_coupon VARCHAR(255) NOT NULL,
    loai_giam ENUM('percent', 'fixed') NOT NULL,
    gia_tri_giam DECIMAL(10,2) NOT NULL,
    gia_tri_toi_da DECIMAL(10,2), -- Giá trị giảm tối đa (cho loại percent)
    don_hang_toi_thieu DECIMAL(10,2) DEFAULT 0,
    so_luong_su_dung INT DEFAULT 0,
    gioi_han_su_dung INT, -- NULL = không giới hạn
    ngay_bat_dau DATE NOT NULL,
    ngay_ket_thuc DATE NOT NULL,
    trang_thai ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Bảng Sử Dụng Coupon
CREATE TABLE coupon_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    coupon_id INT NOT NULL,
    user_id INT NOT NULL,
    order_id INT NOT NULL,
    gia_tri_giam DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
);

-- Thêm Dữ Liệu Mẫu

-- Thêm Admin Mặc Định
INSERT INTO users (ho_ten, email, mat_khau, vai_tro, trang_thai, email_verified_at) VALUES
('Quản Trị Viên', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', NOW());

-- Thêm Danh Mục Mẫu
INSERT INTO categories (ten_danh_muc, slug, mo_ta, thu_tu) VALUES
('Lập Trình Web', 'lap-trinh-web', 'Các Khóa Học Về Lập Trình Website', 1),
('Thiết Kế Đồ Họa', 'thiet-ke-do-hoa', 'Các Khóa Học Về Thiết Kế Và Đồ Họa', 2),
('Marketing Online', 'marketing-online', 'Các Khóa Học Về Marketing Số', 3),
('Kinh Doanh', 'kinh-doanh', 'Các Khóa Học Về Quản Lý Và Kinh Doanh', 4);

-- Thêm Khóa Học Mẫu
INSERT INTO courses (ten_khoa_hoc, slug, mo_ta_ngan, mo_ta_chi_tiet, gia_goc, gia_khuyen_mai, thoi_gian_hoc, category_id, instructor_id, cap_do, trang_thai, noi_bat) VALUES
('Học PHP Từ Cơ Bản Đến Nâng Cao', 'hoc-php-tu-co-ban-den-nang-cao', 'Khóa Học PHP Toàn Diện Cho Người Mới Bắt Đầu', 'Khóa Học Này Sẽ Giúp Bạn Nắm Vững PHP Từ Những Kiến Thức Cơ Bản Nhất Đến Các Kỹ Thuật Nâng Cao', 1500000, 999000, 40, 1, 1, 'beginner', 'published', TRUE),
('Thiết Kế Website Với HTML/CSS', 'thiet-ke-website-voi-html-css', 'Học Thiết Kế Giao Diện Web Chuyên Nghiệp', 'Khóa Học Giúp Bạn Tạo Ra Những Website Đẹp Mắt Và Responsive', 1200000, 799000, 30, 1, 1, 'beginner', 'published', TRUE);

-- Tạo Index Để Tối Ưu Hiệu Suất
CREATE INDEX idx_courses_category ON courses(category_id);
CREATE INDEX idx_courses_instructor ON courses(instructor_id);
CREATE INDEX idx_courses_status ON courses(trang_thai);
CREATE INDEX idx_enrollments_user ON course_enrollments(user_id);
CREATE INDEX idx_enrollments_course ON course_enrollments(course_id);
CREATE INDEX idx_lessons_course ON course_lessons(course_id);
CREATE INDEX idx_lessons_chapter ON course_lessons(chapter_id);
CREATE INDEX idx_progress_user ON lesson_progress(user_id);
CREATE INDEX idx_reviews_course ON course_reviews(course_id);
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(trang_thai);

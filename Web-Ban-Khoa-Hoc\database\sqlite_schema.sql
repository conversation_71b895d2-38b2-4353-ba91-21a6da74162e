-- C<PERSON> Sở Dữ Liệu SQLite Web Bán Khóa Học

-- Bảng <PERSON>h <PERSON>a Học
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ten_danh_muc TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    mo_ta TEXT,
    hinh_anh TEXT,
    thu_tu INTEGER DEFAULT 0,
    trang_thai TEXT DEFAULT 'active' CHECK (trang_thai IN ('active', 'inactive')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Bảng Người Dùng
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ho_ten TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    mat_khau TEXT NOT NULL,
    so_dien_thoai TEXT,
    dia_chi TEXT,
    avatar TEXT,
    vai_tro TEXT DEFAULT 'student' CHECK (vai_tro IN ('admin', 'instructor', 'student')),
    trang_thai TEXT DEFAULT 'active' CHECK (trang_thai IN ('active', 'inactive', 'banned')),
    email_verified_at DATETIME,
    remember_token TEXT,
    reset_token TEXT,
    reset_token_expires DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Bảng Khóa Học
CREATE TABLE IF NOT EXISTS courses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ten_khoa_hoc TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    mo_ta_ngan TEXT,
    mo_ta_chi_tiet TEXT,
    hinh_anh TEXT,
    video_gioi_thieu TEXT,
    gia_goc REAL NOT NULL,
    gia_khuyen_mai REAL,
    thoi_gian_hoc INTEGER,
    so_bai_hoc INTEGER DEFAULT 0,
    so_hoc_vien INTEGER DEFAULT 0,
    danh_gia_trung_binh REAL DEFAULT 0,
    so_luot_danh_gia INTEGER DEFAULT 0,
    category_id INTEGER,
    instructor_id INTEGER,
    cap_do TEXT DEFAULT 'beginner' CHECK (cap_do IN ('beginner', 'intermediate', 'advanced')),
    trang_thai TEXT DEFAULT 'draft' CHECK (trang_thai IN ('draft', 'published', 'archived')),
    noi_bat INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    FOREIGN KEY (instructor_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Bảng Chương Học
CREATE TABLE IF NOT EXISTS course_chapters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    course_id INTEGER NOT NULL,
    ten_chuong TEXT NOT NULL,
    mo_ta TEXT,
    thu_tu INTEGER DEFAULT 0,
    trang_thai TEXT DEFAULT 'active' CHECK (trang_thai IN ('active', 'inactive')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
);

-- Bảng Bài Học
CREATE TABLE IF NOT EXISTS course_lessons (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    chapter_id INTEGER NOT NULL,
    course_id INTEGER NOT NULL,
    ten_bai_hoc TEXT NOT NULL,
    slug TEXT NOT NULL,
    noi_dung TEXT,
    video_url TEXT,
    thoi_luong INTEGER,
    tai_lieu TEXT,
    thu_tu INTEGER DEFAULT 0,
    mien_phi INTEGER DEFAULT 0,
    trang_thai TEXT DEFAULT 'active' CHECK (trang_thai IN ('active', 'inactive')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chapter_id) REFERENCES course_chapters(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
);

-- Bảng Đăng Ký Khóa Học
CREATE TABLE IF NOT EXISTS course_enrollments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    course_id INTEGER NOT NULL,
    ngay_dang_ky DATETIME DEFAULT CURRENT_TIMESTAMP,
    tien_do REAL DEFAULT 0,
    trang_thai TEXT DEFAULT 'active' CHECK (trang_thai IN ('active', 'completed', 'cancelled')),
    ngay_hoan_thanh DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE(user_id, course_id)
);

-- Bảng Tiến Độ Học Tập
CREATE TABLE IF NOT EXISTS lesson_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    lesson_id INTEGER NOT NULL,
    course_id INTEGER NOT NULL,
    da_hoan_thanh INTEGER DEFAULT 0,
    thoi_gian_xem INTEGER DEFAULT 0,
    lan_cuoi_xem DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES course_lessons(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE(user_id, lesson_id)
);

-- Bảng Đánh Giá Khóa Học
CREATE TABLE IF NOT EXISTS course_reviews (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    course_id INTEGER NOT NULL,
    diem_danh_gia INTEGER NOT NULL CHECK (diem_danh_gia >= 1 AND diem_danh_gia <= 5),
    noi_dung_danh_gia TEXT,
    trang_thai TEXT DEFAULT 'pending' CHECK (trang_thai IN ('pending', 'approved', 'rejected')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE(user_id, course_id)
);

-- Bảng Đơn Hàng
CREATE TABLE IF NOT EXISTS orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ma_don_hang TEXT UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    tong_tien REAL NOT NULL,
    trang_thai TEXT DEFAULT 'pending' CHECK (trang_thai IN ('pending', 'completed', 'cancelled', 'refunded')),
    phuong_thuc_thanh_toan TEXT NOT NULL CHECK (phuong_thuc_thanh_toan IN ('bank_transfer', 'momo', 'zalopay', 'vnpay')),
    ghi_chu TEXT,
    ngay_thanh_toan DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Bảng Chi Tiết Đơn Hàng
CREATE TABLE IF NOT EXISTS order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER NOT NULL,
    course_id INTEGER NOT NULL,
    ten_khoa_hoc TEXT NOT NULL,
    gia REAL NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE
);

-- Bảng Giỏ Hàng
CREATE TABLE IF NOT EXISTS cart_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    course_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE(user_id, course_id)
);

-- Bảng Coupons/Mã Giảm Giá
CREATE TABLE IF NOT EXISTS coupons (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ma_coupon TEXT UNIQUE NOT NULL,
    ten_coupon TEXT NOT NULL,
    loai_giam TEXT NOT NULL CHECK (loai_giam IN ('percent', 'fixed')),
    gia_tri_giam REAL NOT NULL,
    gia_tri_toi_da REAL,
    don_hang_toi_thieu REAL DEFAULT 0,
    so_luong_su_dung INTEGER DEFAULT 0,
    gioi_han_su_dung INTEGER,
    ngay_bat_dau DATE NOT NULL,
    ngay_ket_thuc DATE NOT NULL,
    trang_thai TEXT DEFAULT 'active' CHECK (trang_thai IN ('active', 'inactive')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Bảng Sử Dụng Coupon
CREATE TABLE IF NOT EXISTS coupon_usage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    coupon_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    order_id INTEGER NOT NULL,
    gia_tri_giam REAL NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
);

-- Thêm Dữ Liệu Mẫu

-- Thêm Admin Mặc Định (password: password)
INSERT OR IGNORE INTO users (ho_ten, email, mat_khau, vai_tro, trang_thai, email_verified_at) VALUES
('Quản Trị Viên', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', datetime('now'));

-- Thêm Danh Mục Mẫu
INSERT OR IGNORE INTO categories (ten_danh_muc, slug, mo_ta, thu_tu) VALUES
('Lập Trình Web', 'lap-trinh-web', 'Các Khóa Học Về Lập Trình Website', 1),
('Thiết Kế Đồ Họa', 'thiet-ke-do-hoa', 'Các Khóa Học Về Thiết Kế Và Đồ Họa', 2),
('Marketing Online', 'marketing-online', 'Các Khóa Học Về Marketing Số', 3),
('Kinh Doanh', 'kinh-doanh', 'Các Khóa Học Về Quản Lý Và Kinh Doanh', 4);

-- Thêm Khóa Học Mẫu
INSERT OR IGNORE INTO courses (ten_khoa_hoc, slug, mo_ta_ngan, mo_ta_chi_tiet, gia_goc, gia_khuyen_mai, thoi_gian_hoc, category_id, instructor_id, cap_do, trang_thai, noi_bat) VALUES
('Học PHP Từ Cơ Bản Đến Nâng Cao', 'hoc-php-tu-co-ban-den-nang-cao', 'Khóa Học PHP Toàn Diện Cho Người Mới Bắt Đầu', 'Khóa Học Này Sẽ Giúp Bạn Nắm Vững PHP Từ Những Kiến Thức Cơ Bản Nhất Đến Các Kỹ Thuật Nâng Cao', 1500000, 999000, 40, 1, 1, 'beginner', 'published', 1),
('Thiết Kế Website Với HTML/CSS', 'thiet-ke-website-voi-html-css', 'Học Thiết Kế Giao Diện Web Chuyên Nghiệp', 'Khóa Học Giúp Bạn Tạo Ra Những Website Đẹp Mắt Và Responsive', 1200000, 799000, 30, 1, 1, 'beginner', 'published', 1);

-- Tạo Index Để Tối Ưu Hiệu Suất
CREATE INDEX IF NOT EXISTS idx_courses_category ON courses(category_id);
CREATE INDEX IF NOT EXISTS idx_courses_instructor ON courses(instructor_id);
CREATE INDEX IF NOT EXISTS idx_courses_status ON courses(trang_thai);
CREATE INDEX IF NOT EXISTS idx_enrollments_user ON course_enrollments(user_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_course ON course_enrollments(course_id);
CREATE INDEX IF NOT EXISTS idx_lessons_course ON course_lessons(course_id);
CREATE INDEX IF NOT EXISTS idx_lessons_chapter ON course_lessons(chapter_id);
CREATE INDEX IF NOT EXISTS idx_progress_user ON lesson_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_reviews_course ON course_reviews(course_id);
CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(trang_thai);

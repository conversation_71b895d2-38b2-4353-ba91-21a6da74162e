<!DOCTYPE html>
<html lang="vi" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Favicon icon-->
  <link rel="shortcut icon" type="image/png" href="<?= ASSETS_URL ?>/images/logos/favicon.png" />

  <!-- Core Css -->
  <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/styles.css" />

  <title><?= isset($page_title) ? $page_title . ' - ' : '' ?><?= SITE_NAME ?></title>
</head>

<body>
  <!-- Preloader -->
  <div class="preloader">
    <img src="<?= ASSETS_URL ?>/images/logos/favicon.png" alt="loader" class="lds-ripple img-fluid" />
  </div>
  
  <div id="main-wrapper">
    <div class="position-relative overflow-hidden auth-bg min-vh-100 w-100 d-flex align-items-center justify-content-center">
      <div class="d-flex align-items-center justify-content-center w-100">
        <div class="row justify-content-center w-100 my-5 my-xl-0">
          <div class="col-md-9 d-flex flex-column justify-content-center">
            <div class="card mb-0 bg-body auth-login m-auto w-100">
              <div class="row gx-0">
                <!-- ------------------------------------------------- -->
                <!-- Phần Form -->
                <!-- ------------------------------------------------- -->
                <div class="col-xl-6 border-end">
                  <div class="row justify-content-center py-4">
                    <div class="col-lg-11">
                      <div class="card-body">
                        <a href="<?= SITE_URL ?>" class="text-nowrap logo-img d-block mb-4 w-100">
                          <img src="<?= ASSETS_URL ?>/images/logos/logo.svg" class="dark-logo" alt="Logo" />
                        </a>
                        
                        <?php if (isset($flash_message)): ?>
                          <div class="alert alert-<?= $flash_message['type'] === 'error' ? 'danger' : $flash_message['type'] ?> alert-dismissible fade show" role="alert">
                            <?= htmlspecialchars($flash_message['message']) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Đóng"></button>
                          </div>
                        <?php endif; ?>
                        
                        <!-- Nội dung trang sẽ được chèn vào đây -->
                        <?= $content ?>
                        
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- ------------------------------------------------- -->
                <!-- Phần Hình Ảnh -->
                <!-- ------------------------------------------------- -->
                <div class="col-xl-6 d-none d-xl-block">
                  <div class="row justify-content-center align-items-start h-100">
                    <div class="col-lg-9">
                      <div class="position-relative">
                        <div class="row">
                          <div class="col-12">
                            <h2 class="fs-10 text-white mb-3 lh-sm">
                              Chào Mừng Đến Với <br />
                              <?= SITE_NAME ?>
                            </h2>
                          </div>
                          <div class="col-12">
                            <div class="d-flex align-items-center gap-6">
                              <div class="rounded-circle d-flex align-items-center justify-content-center bg-white-subtle" style="width: 80px; height: 80px;">
                                <i class="fa fa-code text-primary" style="font-size: 2rem;"></i>
                              </div>
                              <div>
                                <h5 class="fs-5 text-white fw-semibold mb-0">Học Lập Trình</h5>
                                <p class="mb-0 text-white op-7 fs-3">Từ Cơ Bản Đến Nâng Cao</p>
                              </div>
                            </div>
                          </div>
                          <div class="col-12 mt-4">
                            <div class="d-flex align-items-center gap-6">
                              <div class="rounded-circle d-flex align-items-center justify-content-center bg-white-subtle" style="width: 80px; height: 80px;">
                                <i class="fa fa-graduation-cap text-success" style="font-size: 2rem;"></i>
                              </div>
                              <div>
                                <h5 class="fs-5 text-white fw-semibold mb-0">Chứng Chỉ</h5>
                                <p class="mb-0 text-white op-7 fs-3">Được Công Nhận Rộng Rãi</p>
                              </div>
                            </div>
                          </div>
                          <div class="col-12 mt-4">
                            <div class="d-flex align-items-center gap-6">
                              <div class="rounded-circle d-flex align-items-center justify-content-center bg-white-subtle" style="width: 80px; height: 80px;">
                                <i class="fa fa-users text-warning" style="font-size: 2rem;"></i>
                              </div>
                              <div>
                                <h5 class="fs-5 text-white fw-semibold mb-0">Cộng Đồng</h5>
                                <p class="mb-0 text-white op-7 fs-3">Hỗ Trợ 24/7</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Import Js Files -->
  <script src="<?= ASSETS_URL ?>/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="<?= ASSETS_URL ?>/libs/simplebar/dist/simplebar.min.js"></script>
  <script src="<?= ASSETS_URL ?>/js/theme/app.init.js"></script>
  <script src="<?= ASSETS_URL ?>/js/theme/theme.js"></script>
  <script src="<?= ASSETS_URL ?>/js/theme/app.min.js"></script>
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  
  <?php if (isset($additional_js)): ?>
    <?= $additional_js ?>
  <?php endif; ?>
</body>

</html>

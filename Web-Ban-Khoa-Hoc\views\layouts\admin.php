<!DOCTYPE html>
<html lang="vi" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <link rel="shortcut icon" type="image/png" href="<?= ASSETS_URL ?>/images/logos/favicon.png" />
  <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/styles.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <title><?= isset($page_title) ? $page_title . ' - ' : '' ?>Admin - <?= SITE_NAME ?></title>
</head>

<body class="link-sidebar">
  <!-- Preloader -->
  <div class="preloader">
    <img src="<?= ASSETS_URL ?>/images/logos/favicon.png" alt="loader" class="lds-ripple img-fluid" />
  </div>
  
  <div id="main-wrapper">
    <!-- Sidebar Start -->
    <aside class="left-sidebar with-vertical">
      <div>
        <div class="brand-logo d-flex align-items-center">
          <a href="dashboard.php" class="text-nowrap logo-img">
            <img src="<?= ASSETS_URL ?>/images/logos/logo.svg" alt="Logo" />
          </a>
        </div>

        <nav class="sidebar-nav scroll-sidebar" data-simplebar>
          <ul id="sidebarnav">
            <li class="nav-small-cap">
              <i class="fa fa-dots-horizontal nav-small-cap-icon fs-4"></i>
              <span class="hide-menu">Trang Chủ</span>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="dashboard.php" aria-expanded="false">
                <span><i class="fa fa-home"></i></span>
                <span class="hide-menu">Bảng Điều Khiển</span>
              </a>
            </li>

            <li class="nav-small-cap">
              <i class="fa fa-dots-horizontal nav-small-cap-icon fs-4"></i>
              <span class="hide-menu">Quản Lý Khóa Học</span>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="courses.php" aria-expanded="false">
                <span><i class="fa fa-graduation-cap"></i></span>
                <span class="hide-menu">Khóa Học</span>
              </a>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="categories.php" aria-expanded="false">
                <span><i class="fa fa-tags"></i></span>
                <span class="hide-menu">Danh Mục</span>
              </a>
            </li>

            <li class="nav-small-cap">
              <i class="fa fa-dots-horizontal nav-small-cap-icon fs-4"></i>
              <span class="hide-menu">Quản Lý Người Dùng</span>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="users.php" aria-expanded="false">
                <span><i class="fa fa-users"></i></span>
                <span class="hide-menu">Người Dùng</span>
              </a>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="instructors.php" aria-expanded="false">
                <span><i class="fa fa-chalkboard-teacher"></i></span>
                <span class="hide-menu">Giảng Viên</span>
              </a>
            </li>

            <li class="nav-small-cap">
              <i class="fa fa-dots-horizontal nav-small-cap-icon fs-4"></i>
              <span class="hide-menu">Bán Hàng</span>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="orders.php" aria-expanded="false">
                <span><i class="fa fa-shopping-cart"></i></span>
                <span class="hide-menu">Đơn Hàng</span>
              </a>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="coupons.php" aria-expanded="false">
                <span><i class="fa fa-ticket-alt"></i></span>
                <span class="hide-menu">Mã Giảm Giá</span>
              </a>
            </li>

            <li class="nav-small-cap">
              <i class="fa fa-dots-horizontal nav-small-cap-icon fs-4"></i>
              <span class="hide-menu">Báo Cáo</span>
            </li>
            
            <li class="sidebar-item">
              <a class="sidebar-link" href="reports.php" aria-expanded="false">
                <span><i class="fa fa-chart-bar"></i></span>
                <span class="hide-menu">Thống Kê</span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </aside>
    <!-- Sidebar End -->

    <!-- Main wrapper -->
    <div class="body-wrapper">
      <!-- Header Start -->
      <header class="app-header">
        <nav class="navbar navbar-expand-lg navbar-light">
          <ul class="navbar-nav">
            <li class="nav-item">
              <a class="nav-link sidebartoggler nav-icon-hover ms-n3" id="headerCollapse" href="javascript:void(0)">
                <i class="fa fa-bars"></i>
              </a>
            </li>
          </ul>
          
          <div class="d-block d-lg-none">
            <img src="<?= ASSETS_URL ?>/images/logos/logo.svg" class="dark-logo" width="180" alt="" />
          </div>
          
          <button class="navbar-toggler p-0 border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="p-2">
              <i class="fa fa-bars fs-4 text-dark"></i>
            </span>
          </button>
          
          <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
            <div class="d-flex align-items-center justify-content-between">
              <ul class="navbar-nav flex-row ms-auto align-items-center justify-content-center">
                <li class="nav-item dropdown">
                  <a class="nav-link nav-icon-hover" href="javascript:void(0)" id="drop2" data-bs-toggle="dropdown" aria-expanded="false">
                    <img src="<?= ASSETS_URL ?>/images/profile/user-1.jpg" alt="" width="35" height="35" class="rounded-circle">
                  </a>
                  <div class="dropdown-menu dropdown-menu-end dropdown-menu-animate-up" aria-labelledby="drop2">
                    <div class="message-body">
                      <a href="profile.php" class="d-flex align-items-center gap-2 dropdown-item">
                        <i class="fa fa-user fs-6"></i>
                        <p class="mb-0 fs-3">Hồ Sơ Của Tôi</p>
                      </a>
                      <a href="<?= SITE_URL ?>" class="d-flex align-items-center gap-2 dropdown-item" target="_blank">
                        <i class="fa fa-globe fs-6"></i>
                        <p class="mb-0 fs-3">Xem Website</p>
                      </a>
                      <a href="../auth/logout.php" class="btn btn-outline-primary mx-3 mt-2 d-block">
                        <i class="fa fa-sign-out-alt me-1"></i>Đăng Xuất
                      </a>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </nav>
      </header>
      <!-- Header End -->

      <div class="container-fluid">
        <?php if (isset($flash_message)): ?>
          <div class="alert alert-<?= $flash_message['type'] === 'error' ? 'danger' : $flash_message['type'] ?> alert-dismissible fade show" role="alert">
            <?= $flash_message['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Đóng"></button>
          </div>
        <?php endif; ?>

        <!-- Nội dung trang -->
        <div class="body-wrapper-inner">
          <?= $content ?>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="<?= ASSETS_URL ?>/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="<?= ASSETS_URL ?>/libs/simplebar/dist/simplebar.min.js"></script>
  <script src="<?= ASSETS_URL ?>/js/theme/app.init.js"></script>
  <script src="<?= ASSETS_URL ?>/js/theme/theme.js"></script>
  <script src="<?= ASSETS_URL ?>/js/theme/app.min.js"></script>
  <script src="<?= ASSETS_URL ?>/js/theme/sidebarmenu-default.js"></script>

  <!-- Custom JavaScript -->
  <script>
    // Toggle sidebar trên mobile
    document.addEventListener('DOMContentLoaded', function() {
      const sidebarToggler = document.getElementById('headerCollapse');
      const sidebar = document.querySelector('.left-sidebar');
      const bodyWrapper = document.querySelector('.body-wrapper');

      if (sidebarToggler && sidebar) {
        sidebarToggler.addEventListener('click', function(e) {
          e.preventDefault();

          if (window.innerWidth <= 1199) {
            sidebar.classList.toggle('show');
          } else {
            // Desktop: toggle margin
            if (bodyWrapper.style.marginLeft === '0px' || bodyWrapper.style.marginLeft === '') {
              bodyWrapper.style.marginLeft = '270px';
              sidebar.style.transform = 'translateX(0)';
            } else {
              bodyWrapper.style.marginLeft = '0px';
              sidebar.style.transform = 'translateX(-100%)';
            }
          }
        });
      }

      // Đóng sidebar khi click outside trên mobile
      document.addEventListener('click', function(e) {
        if (window.innerWidth <= 1199) {
          if (!sidebar.contains(e.target) && !sidebarToggler.contains(e.target)) {
            sidebar.classList.remove('show');
          }
        }
      });

      // Handle resize
      window.addEventListener('resize', function() {
        if (window.innerWidth > 1199) {
          sidebar.classList.remove('show');
          bodyWrapper.style.marginLeft = '270px';
          sidebar.style.transform = 'translateX(0)';
        } else {
          bodyWrapper.style.marginLeft = '0px';
          sidebar.style.transform = 'translateX(-100%)';
        }
      });
    });
  </script>

  <?php if (isset($additional_js)): ?>
    <?= $additional_js ?>
  <?php endif; ?>

  <!-- Custom CSS để fix layout -->
  <style>
    /* Fix khoảng cách sidebar và content */
    .body-wrapper {
      margin-left: 270px; /* Chiều rộng sidebar */
      transition: margin-left 0.3s ease;
    }

    .left-sidebar {
      width: 270px;
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      z-index: 1000;
    }

    .body-wrapper-inner {
      padding: 20px;
      min-height: calc(100vh - 80px);
    }

    /* Responsive cho mobile */
    @media (max-width: 1199px) {
      .body-wrapper {
        margin-left: 0;
      }

      .left-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }

      .left-sidebar.show {
        transform: translateX(0);
      }
    }

    /* Header spacing */
    .app-header {
      padding-left: 0;
      padding-right: 0;
    }

    /* Card spacing */
    .card {
      margin-bottom: 24px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border: 1px solid #e9ecef;
    }

    /* Breadcrumb styling */
    .breadcrumb {
      background: transparent;
      padding: 0;
      margin: 0;
    }

    /* Table responsive */
    .table-responsive {
      border-radius: 8px;
      overflow: hidden;
    }

    /* Button spacing */
    .btn-group .btn {
      margin-right: 2px;
    }

    .btn-group .btn:last-child {
      margin-right: 0;
    }
  </style>
</body>

</html>

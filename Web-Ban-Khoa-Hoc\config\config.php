<?php
// <PERSON><PERSON><PERSON> Cho Website B<PERSON>

// Môi trường development
define('DEVELOPMENT', true);

// Đường Dẫn <PERSON>h<PERSON>
define('ROOT_PATH', dirname(__DIR__));

// Thông Tin Cơ Sở Dữ Liệu SQLite
define('DB_TYPE', 'sqlite');
define('DB_PATH', ROOT_PATH . '/database/web_ban_khoa_hoc.db');

// Cấu H<PERSON>nh Website
define('SITE_NAME', 'Học Viện Trự<PERSON>ến');
define('SITE_URL', 'http://localhost:8000');
define('ADMIN_EMAIL', '<EMAIL>');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('VIEWS_PATH', ROOT_PATH . '/views');
define('INCLUDES_PATH', ROOT_PATH . '/includes');

// URL T<PERSON><PERSON>
define('ASSETS_URL', SITE_URL . '/assets');
define('UPLOADS_URL', SITE_URL . '/uploads');

// Cấu Hình Bảo Mật
define('HASH_ALGORITHM', 'sha256');
define('SESSION_LIFETIME', 3600); // 1 giờ
define('CSRF_TOKEN_LENGTH', 32);

// Cấu Hình Upload File
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_VIDEO_TYPES', ['mp4', 'avi', 'mov', 'wmv']);

// Cấu Hình Phân Trang
define('ITEMS_PER_PAGE', 12);
define('ADMIN_ITEMS_PER_PAGE', 20);

// Cấu Hình Email
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// Múi Giờ
date_default_timezone_set('Asia/Ho_Chi_Minh');

// Bắt Đầu Session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Hàm Hiển Thị Lỗi (Chỉ Trong Môi Trường Development)
if (defined('DEVELOPMENT') && DEVELOPMENT === true) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}
?>

<?php
require_once 'includes/functions.php';

// <PERSON><PERSON><PERSON> tra remember token
if (!isLoggedIn() && isset($_COOKIE['remember_token'])) {
    global $database;
    $user = $database->selectOne(
        "SELECT * FROM users WHERE remember_token = ? AND trang_thai = 'active'",
        [$_COOKIE['remember_token']]
    );
    
    if ($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['ho_ten'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = $user['vai_tro'];
    }
}

// Nếu đã đăng nhập, chuyển hướng đến dashboard
if (isLoggedIn()) {
    if ($_SESSION['user_role'] === 'admin') {
        redirect(SITE_URL . '/views/admin/dashboard.php');
    } else {
        redirect(SITE_URL . '/views/user/dashboard.php');
    }
}

// <PERSON><PERSON>y kh<PERSON><PERSON> học nổi bật
global $database;
$featured_courses = $database->select(
    "SELECT c.*, cat.ten_danh_muc, u.ho_ten as ten_giang_vien 
     FROM courses c 
     LEFT JOIN categories cat ON c.category_id = cat.id 
     LEFT JOIN users u ON c.instructor_id = u.id 
     WHERE c.trang_thai = 'published' AND c.noi_bat = 1 
     ORDER BY c.created_at DESC 
     LIMIT 6"
);

// Lấy danh mục
$categories = $database->select(
    "SELECT * FROM categories WHERE trang_thai = 'active' ORDER BY thu_tu ASC"
);

$page_title = 'Trang Chủ';
?>

<!DOCTYPE html>
<html lang="vi" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <link rel="shortcut icon" type="image/png" href="<?= ASSETS_URL ?>/images/logos/favicon.png" />
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/styles.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <title><?= $page_title ?> - <?= SITE_NAME ?></title>
</head>

<body>
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container">
                <a class="navbar-brand" href="<?= SITE_URL ?>">
                    <img src="<?= ASSETS_URL ?>/images/logos/logo.svg" alt="<?= SITE_NAME ?>" height="40">
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="<?= SITE_URL ?>">Trang Chủ</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                Khóa Học
                            </a>
                            <ul class="dropdown-menu">
                                <?php foreach ($categories as $category): ?>
                                    <li><a class="dropdown-item" href="courses.php?category=<?= $category['slug'] ?>">
                                        <?= htmlspecialchars($category['ten_danh_muc']) ?>
                                    </a></li>
                                <?php endforeach; ?>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="about.php">Giới Thiệu</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="contact.php">Liên Hệ</a>
                        </li>
                    </ul>
                    
                    <div class="d-flex">
                        <a href="views/auth/login.php" class="btn btn-outline-primary me-2">
                            <i class="fa fa-sign-in-alt me-1"></i>Đăng Nhập
                        </a>
                        <a href="views/auth/register.php" class="btn btn-primary">
                            <i class="fa fa-user-plus me-1"></i>Đăng Ký
                        </a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="bg-primary text-white py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">Học Lập Trình Online Cùng <?= SITE_NAME ?></h1>
                    <p class="lead mb-4">Nâng Cao Kỹ Năng Lập Trình Của Bạn Với Các Khóa Học Chất Lượng Cao Từ Các Chuyên Gia Hàng Đầu</p>
                    <div class="d-flex gap-3">
                        <a href="views/auth/register.php" class="btn btn-light btn-lg">
                            <i class="fa fa-rocket me-2"></i>Bắt Đầu Ngay
                        </a>
                        <a href="#featured-courses" class="btn btn-outline-light btn-lg">
                            <i class="fa fa-play me-2"></i>Xem Khóa Học
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="fa fa-code" style="font-size: 15rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Courses -->
    <section id="featured-courses" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">Khóa Học Nổi Bật</h2>
                <p class="text-muted">Các Khóa Học Được Yêu Thích Nhất</p>
            </div>
            
            <div class="row">
                <?php foreach ($featured_courses as $course): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            <img src="<?= UPLOADS_URL ?>/courses/<?= $course['hinh_anh'] ?: 'default.jpg' ?>" 
                                 class="card-img-top" alt="<?= htmlspecialchars($course['ten_khoa_hoc']) ?>" style="height: 200px; object-fit: cover;">
                            <div class="card-body d-flex flex-column">
                                <span class="badge bg-primary mb-2"><?= htmlspecialchars($course['ten_danh_muc']) ?></span>
                                <h5 class="card-title"><?= htmlspecialchars($course['ten_khoa_hoc']) ?></h5>
                                <p class="card-text text-muted flex-grow-1"><?= htmlspecialchars($course['mo_ta_ngan']) ?></p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <?php if ($course['gia_khuyen_mai']): ?>
                                            <span class="text-decoration-line-through text-muted"><?= formatCurrency($course['gia_goc']) ?></span>
                                            <span class="fw-bold text-primary"><?= formatCurrency($course['gia_khuyen_mai']) ?></span>
                                        <?php else: ?>
                                            <span class="fw-bold text-primary"><?= formatCurrency($course['gia_goc']) ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <i class="fa fa-clock me-1"></i><?= $course['thoi_gian_hoc'] ?> giờ
                                    </small>
                                </div>
                                <a href="course-detail.php?slug=<?= $course['slug'] ?>" class="btn btn-primary mt-3">
                                    <i class="fa fa-eye me-1"></i>Xem Chi Tiết
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><?= SITE_NAME ?></h5>
                    <p>Nền Tảng Học Lập Trình Online Hàng Đầu Việt Nam</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; <?= date('Y') ?> <?= SITE_NAME ?>. Tất Cả Quyền Được Bảo Lưu.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="<?= ASSETS_URL ?>/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>

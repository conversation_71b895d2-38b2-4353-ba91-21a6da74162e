<?php
require_once '../../includes/functions.php';

$token = $_GET['token'] ?? '';

// Kiểm tra token
if (empty($token)) {
    setFlashMessage('error', 'Token Không Hợp Lệ');
    redirect(SITE_URL . '/views/auth/forgot-password.php');
}

global $database;
$user = $database->selectOne(
    "SELECT * FROM users WHERE reset_token = ? AND reset_token_expires > NOW() AND trang_thai = 'active'",
    [$token]
);

if (!$user) {
    setFlashMessage('error', 'Token Không Hợp Lệ Hoặc Đã Hết Hạn');
    redirect(SITE_URL . '/views/auth/forgot-password.php');
}

// Xử lý đặt lại mật khẩu
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // Validate
        if (empty($password) || empty($confirm_password)) {
            throw new Exception('<PERSON><PERSON> Thông Tin');
        }
        
        if (strlen($password) < 6) {
            throw new Exception('Mật Khẩu Phải Có Ít Nhất 6 Ký Tự');
        }
        
        if ($password !== $confirm_password) {
            throw new Exception('Mật Khẩu Xác Nhận Không Khớp');
        }
        
        // Cập nhật mật khẩu mới
        $database->update(
            "UPDATE users SET mat_khau = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?",
            [hashPassword($password), $user['id']]
        );
        
        setFlashMessage('success', 'Đặt Lại Mật Khẩu Thành Công! Vui Lòng Đăng Nhập Với Mật Khẩu Mới');
        redirect(SITE_URL . '/views/auth/login.php');
        
    } catch (Exception $e) {
        $flash_message = ['type' => 'error', 'message' => $e->getMessage()];
    }
}

// Lấy flash message từ session
if (!isset($flash_message)) {
    $flash_message = getFlashMessage();
}

$page_title = 'Đặt Lại Mật Khẩu';

// Nội dung trang
ob_start();
?>

<h2 class="lh-base mb-4">Đặt Lại Mật Khẩu</h2>
<p class="mb-4 text-muted">Nhập Mật Khẩu Mới Cho Tài Khoản: <strong><?= htmlspecialchars($user['email']) ?></strong></p>

<form method="POST" action="">
    <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
    
    <div class="mb-3">
        <label for="password" class="form-label">Mật Khẩu Mới</label>
        <input type="password" class="form-control" id="password" name="password" 
               placeholder="Nhập Mật Khẩu Mới (Ít Nhất 6 Ký Tự)" required>
    </div>
    
    <div class="mb-4">
        <label for="confirm_password" class="form-label">Xác Nhận Mật Khẩu</label>
        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
               placeholder="Nhập Lại Mật Khẩu Mới" required>
    </div>
    
    <button type="submit" class="btn btn-dark w-100 py-8 mb-4 rounded-1">
        <i class="fa fa-key me-2"></i>Đặt Lại Mật Khẩu
    </button>
    
    <div class="d-flex align-items-center justify-content-center">
        <a class="text-primary fw-bolder" href="login.php">
            <i class="fa fa-arrow-left me-2"></i>Quay Lại Đăng Nhập
        </a>
    </div>
</form>

<?php
$content = ob_get_clean();

// Include layout
include '../layouts/auth.php';
?>

<script>
// Kiểm tra mật khẩu khớp
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Mật Khẩu Xác Nhận Không Khớp');
    } else {
        this.setCustomValidity('');
    }
});
</script>

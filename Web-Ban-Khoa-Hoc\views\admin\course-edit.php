<?php
require_once '../../includes/functions.php';

// Kiểm tra đăng nhập và quyền admin
if (!isLoggedIn() || !isAdmin()) {
    setFlashMessage('error', 'Bạn Không <PERSON>ề<PERSON>y <PERSON>ập Trang <PERSON>à<PERSON>');
    redirect(SITE_URL . '/views/auth/login.php');
}

$course_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$course_id) {
    setFlashMessage('error', '<PERSON>ô<PERSON>ông Tồn Tại');
    redirect('courses.php');
}

global $database;

// Lấy thông tin môn học
$course = $database->selectOne("SELECT * FROM courses WHERE id = ?", [$course_id]);
if (!$course) {
    setFlashMessage('error', '<PERSON>ô<PERSON> Tồn Tại');
    redirect('courses.php');
}

// Xử lý cập nhật môn học
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate CSRF token
        if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Token Bảo Mật Không Hợp Lệ');
        }

        $ten_khoa_hoc = sanitize($_POST['ten_khoa_hoc'] ?? '');
        $mo_ta_ngan = sanitize($_POST['mo_ta_ngan'] ?? '');
        $mo_ta_chi_tiet = $_POST['mo_ta_chi_tiet'] ?? '';
        $gia_goc = (float)($_POST['gia_goc'] ?? 0);
        $gia_khuyen_mai = !empty($_POST['gia_khuyen_mai']) ? (float)$_POST['gia_khuyen_mai'] : null;
        $thoi_gian_hoc = (int)($_POST['thoi_gian_hoc'] ?? 0);
        $category_id = !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null;
        $instructor_id = !empty($_POST['instructor_id']) ? (int)$_POST['instructor_id'] : null;
        $cap_do = sanitize($_POST['cap_do'] ?? 'beginner');
        $trang_thai = sanitize($_POST['trang_thai'] ?? 'draft');
        $noi_bat = isset($_POST['noi_bat']) ? 1 : 0;

        // Validate
        if (empty($ten_khoa_hoc)) {
            throw new Exception('Vui Lòng Nhập Tên Môn Học');
        }

        if ($gia_goc <= 0) {
            throw new Exception('Giá Gốc Phải Lớn Hơn 0');
        }

        if ($gia_khuyen_mai && $gia_khuyen_mai >= $gia_goc) {
            throw new Exception('Giá Khuyến Mãi Phải Nhỏ Hơn Giá Gốc');
        }

        // Tạo slug nếu tên thay đổi
        $slug = $course['slug'];
        if ($ten_khoa_hoc !== $course['ten_khoa_hoc']) {
            $new_slug = createSlug($ten_khoa_hoc);
            $existing = $database->selectOne("SELECT id FROM courses WHERE slug = ? AND id != ?", [$new_slug, $course_id]);
            if (!$existing) {
                $slug = $new_slug;
            }
        }

        // Xử lý upload hình ảnh mới
        $hinh_anh = $course['hinh_anh'];
        if (isset($_FILES['hinh_anh']) && $_FILES['hinh_anh']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = ROOT_PATH . '/uploads/courses';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            // Xóa hình cũ
            if ($hinh_anh && file_exists($upload_dir . '/' . $hinh_anh)) {
                unlink($upload_dir . '/' . $hinh_anh);
            }
            
            $hinh_anh = uploadFile($_FILES['hinh_anh'], $upload_dir, ALLOWED_IMAGE_TYPES);
        }

        // Cập nhật database
        $database->update(
            "UPDATE courses SET ten_khoa_hoc = ?, slug = ?, mo_ta_ngan = ?, mo_ta_chi_tiet = ?, hinh_anh = ?, 
                               gia_goc = ?, gia_khuyen_mai = ?, thoi_gian_hoc = ?, category_id = ?, instructor_id = ?, 
                               cap_do = ?, trang_thai = ?, noi_bat = ?, updated_at = datetime('now') 
             WHERE id = ?",
            [$ten_khoa_hoc, $slug, $mo_ta_ngan, $mo_ta_chi_tiet, $hinh_anh, $gia_goc, $gia_khuyen_mai, 
             $thoi_gian_hoc, $category_id, $instructor_id, $cap_do, $trang_thai, $noi_bat, $course_id]
        );

        // Cập nhật lại thông tin course
        $course = $database->selectOne("SELECT * FROM courses WHERE id = ?", [$course_id]);

        setFlashMessage('success', 'Cập Nhật Môn Học Thành Công');

    } catch (Exception $e) {
        $flash_message = ['type' => 'error', 'message' => $e->getMessage()];
    }
}

// Lấy danh mục
$categories = $database->select("SELECT * FROM categories WHERE trang_thai = 'active' ORDER BY ten_danh_muc");

// Lấy danh sách giảng viên
$instructors = $database->select("SELECT * FROM users WHERE vai_tro IN ('admin', 'instructor') ORDER BY ho_ten");

// Lấy thống kê môn học
$stats = [
    'chapters' => $database->count('course_chapters', 'course_id = ?', [$course_id]),
    'lessons' => $database->count('course_lessons', 'course_id = ?', [$course_id]),
    'enrollments' => $database->count('course_enrollments', 'course_id = ?', [$course_id])
];

$flash_message = $flash_message ?? getFlashMessage();
$page_title = 'Chỉnh Sửa Môn Học: ' . $course['ten_khoa_hoc'];

// Nội dung trang
ob_start();
?>

<div class="row">
  <div class="col-12">
    <!-- Thống kê nhanh -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card bg-primary text-white">
          <div class="card-body text-center">
            <h3 class="mb-1"><?= $stats['chapters'] ?></h3>
            <p class="mb-0">Chương</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-success text-white">
          <div class="card-body text-center">
            <h3 class="mb-1"><?= $stats['lessons'] ?></h3>
            <p class="mb-0">Bài Học</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-info text-white">
          <div class="card-body text-center">
            <h3 class="mb-1"><?= $stats['enrollments'] ?></h3>
            <p class="mb-0">Học Viên</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-warning text-white">
          <div class="card-body text-center">
            <h3 class="mb-1"><?= number_format($course['danh_gia_trung_binh'], 1) ?></h3>
            <p class="mb-0">Đánh Giá</p>
          </div>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
          <i class="fa fa-edit me-2"></i>Chỉnh Sửa Môn Học
        </h5>
        <div>
          <a href="course-chapters.php?course_id=<?= $course_id ?>" class="btn btn-info me-2">
            <i class="fa fa-list me-1"></i>Quản Lý Chương
          </a>
          <a href="courses.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left me-1"></i>Quay Lại
          </a>
        </div>
      </div>
      
      <form method="POST" enctype="multipart/form-data">
        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
        
        <div class="card-body">
          <div class="row">
            <!-- Thông tin cơ bản -->
            <div class="col-lg-8">
              <div class="mb-3">
                <label for="ten_khoa_hoc" class="form-label">Tên Môn Học <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="ten_khoa_hoc" name="ten_khoa_hoc" 
                       value="<?= htmlspecialchars($course['ten_khoa_hoc']) ?>" required>
              </div>

              <div class="mb-3">
                <label for="mo_ta_ngan" class="form-label">Mô Tả Ngắn</label>
                <textarea class="form-control" id="mo_ta_ngan" name="mo_ta_ngan" rows="3" 
                          placeholder="Mô tả ngắn gọn về môn học..."><?= htmlspecialchars($course['mo_ta_ngan']) ?></textarea>
              </div>

              <div class="mb-3">
                <label for="mo_ta_chi_tiet" class="form-label">Mô Tả Chi Tiết</label>
                <textarea class="form-control" id="mo_ta_chi_tiet" name="mo_ta_chi_tiet" rows="8" 
                          placeholder="Mô tả chi tiết về nội dung, mục tiêu học tập..."><?= htmlspecialchars($course['mo_ta_chi_tiet']) ?></textarea>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="gia_goc" class="form-label">Giá Gốc (VNĐ) <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="gia_goc" name="gia_goc" 
                           value="<?= $course['gia_goc'] ?>" min="0" step="1000" required>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label for="gia_khuyen_mai" class="form-label">Giá Khuyến Mãi (VNĐ)</label>
                    <input type="number" class="form-control" id="gia_khuyen_mai" name="gia_khuyen_mai" 
                           value="<?= $course['gia_khuyen_mai'] ?>" min="0" step="1000">
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <label for="thoi_gian_hoc" class="form-label">Thời Gian Học (Giờ)</label>
                <input type="number" class="form-control" id="thoi_gian_hoc" name="thoi_gian_hoc" 
                       value="<?= $course['thoi_gian_hoc'] ?>" min="0">
              </div>
            </div>

            <!-- Cài đặt -->
            <div class="col-lg-4">
              <div class="mb-3">
                <label class="form-label">Hình Ảnh Hiện Tại</label>
                <?php if ($course['hinh_anh']): ?>
                  <div class="mb-2">
                    <img src="<?= UPLOADS_URL ?>/courses/<?= $course['hinh_anh'] ?>" 
                         alt="<?= htmlspecialchars($course['ten_khoa_hoc']) ?>" 
                         class="img-thumbnail" style="max-width: 200px;">
                  </div>
                <?php endif; ?>
                <input type="file" class="form-control" id="hinh_anh" name="hinh_anh" accept="image/*">
                <div class="form-text">Định dạng: JPG, PNG, GIF. Tối đa 5MB</div>
              </div>

              <div class="mb-3">
                <label for="category_id" class="form-label">Danh Mục</label>
                <select class="form-select" id="category_id" name="category_id">
                  <option value="">Chọn Danh Mục</option>
                  <?php foreach ($categories as $category): ?>
                    <option value="<?= $category['id'] ?>" 
                            <?= $course['category_id'] == $category['id'] ? 'selected' : '' ?>>
                      <?= htmlspecialchars($category['ten_danh_muc']) ?>
                    </option>
                  <?php endforeach; ?>
                </select>
              </div>

              <div class="mb-3">
                <label for="instructor_id" class="form-label">Giảng Viên</label>
                <select class="form-select" id="instructor_id" name="instructor_id">
                  <option value="">Chọn Giảng Viên</option>
                  <?php foreach ($instructors as $instructor): ?>
                    <option value="<?= $instructor['id'] ?>" 
                            <?= $course['instructor_id'] == $instructor['id'] ? 'selected' : '' ?>>
                      <?= htmlspecialchars($instructor['ho_ten']) ?>
                    </option>
                  <?php endforeach; ?>
                </select>
              </div>

              <div class="mb-3">
                <label for="cap_do" class="form-label">Cấp Độ</label>
                <select class="form-select" id="cap_do" name="cap_do">
                  <option value="beginner" <?= $course['cap_do'] === 'beginner' ? 'selected' : '' ?>>
                    Cơ Bản
                  </option>
                  <option value="intermediate" <?= $course['cap_do'] === 'intermediate' ? 'selected' : '' ?>>
                    Trung Cấp
                  </option>
                  <option value="advanced" <?= $course['cap_do'] === 'advanced' ? 'selected' : '' ?>>
                    Nâng Cao
                  </option>
                </select>
              </div>

              <div class="mb-3">
                <label for="trang_thai" class="form-label">Trạng Thái</label>
                <select class="form-select" id="trang_thai" name="trang_thai">
                  <option value="draft" <?= $course['trang_thai'] === 'draft' ? 'selected' : '' ?>>
                    Nháp
                  </option>
                  <option value="published" <?= $course['trang_thai'] === 'published' ? 'selected' : '' ?>>
                    Đã Xuất Bản
                  </option>
                  <option value="archived" <?= $course['trang_thai'] === 'archived' ? 'selected' : '' ?>>
                    Lưu Trữ
                  </option>
                </select>
              </div>

              <div class="mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="noi_bat" name="noi_bat" 
                         <?= $course['noi_bat'] ? 'checked' : '' ?>>
                  <label class="form-check-label" for="noi_bat">
                    Đánh Dấu Nổi Bật
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="card-footer">
          <div class="d-flex justify-content-between">
            <a href="courses.php" class="btn btn-secondary">
              <i class="fa fa-arrow-left me-1"></i>Quay Lại
            </a>
            <button type="submit" class="btn btn-primary">
              <i class="fa fa-save me-1"></i>Cập Nhật Môn Học
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<?php
$content = ob_get_clean();

// Include layout
include '../layouts/admin.php';
?>

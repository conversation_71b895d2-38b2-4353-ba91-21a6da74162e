<?php
require_once '../../includes/functions.php';

// Kiểm tra đăng nhập và quyền admin
if (!isLoggedIn() || !isAdmin()) {
    setFlashMessage('error', 'Bạn Không <PERSON>ó <PERSON>ền T<PERSON>y Cập Trang Này');
    redirect(SITE_URL . '/views/auth/login.php');
}

// Lấy thống kê
global $database;

$stats = [
    'total_users' => $database->count('users'),
    'total_courses' => $database->count('courses'),
    'total_orders' => $database->count('orders'),
    'total_revenue' => $database->selectOne("SELECT SUM(tong_tien) as total FROM orders WHERE trang_thai = 'completed'")['total'] ?? 0
];

// Lấy đơn hàng gần đây
$recent_orders = $database->select(
    "SELECT o.*, u.ho_ten 
     FROM orders o 
     LEFT JOIN users u ON o.user_id = u.id 
     ORDER BY o.created_at DESC 
     LIMIT 5"
);

// Lấy khóa học mới
$recent_courses = $database->select(
    "SELECT c.*, cat.ten_danh_muc 
     FROM courses c 
     LEFT JOIN categories cat ON c.category_id = cat.id 
     ORDER BY c.created_at DESC 
     LIMIT 5"
);

$flash_message = getFlashMessage();
$page_title = 'Bảng Điều Khiển';

// Nội dung trang
ob_start();
?>

<div class="row">
  <div class="col-lg-12">
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
      <div class="card-body px-4 py-3">
        <div class="row align-items-center">
          <div class="col-9">
            <h4 class="fw-semibold mb-8">Chào Mừng <?= htmlspecialchars($_SESSION['user_name']) ?>!</h4>
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a class="text-muted" href="dashboard.php">Bảng Điều Khiển</a></li>
              </ol>
            </nav>
          </div>
          <div class="col-3">
            <div class="text-center mb-n5">
              <img src="<?= ASSETS_URL ?>/images/breadcrumb/ChatBc.png" alt="" class="img-fluid mb-n4">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Thống kê tổng quan -->
<div class="row">
  <div class="col-lg-3 col-md-6">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="me-4">
            <span class="round-8 bg-primary rounded-circle d-inline-block"></span>
          </div>
          <div>
            <h4 class="card-title mb-0"><?= number_format($stats['total_users']) ?></h4>
            <p class="card-subtitle">Tổng Người Dùng</p>
          </div>
          <div class="ms-auto">
            <i class="fa fa-users text-primary" style="font-size: 2rem;"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="col-lg-3 col-md-6">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="me-4">
            <span class="round-8 bg-success rounded-circle d-inline-block"></span>
          </div>
          <div>
            <h4 class="card-title mb-0"><?= number_format($stats['total_courses']) ?></h4>
            <p class="card-subtitle">Tổng Khóa Học</p>
          </div>
          <div class="ms-auto">
            <i class="fa fa-graduation-cap text-success" style="font-size: 2rem;"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="col-lg-3 col-md-6">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="me-4">
            <span class="round-8 bg-warning rounded-circle d-inline-block"></span>
          </div>
          <div>
            <h4 class="card-title mb-0"><?= number_format($stats['total_orders']) ?></h4>
            <p class="card-subtitle">Tổng Đơn Hàng</p>
          </div>
          <div class="ms-auto">
            <i class="fa fa-shopping-cart text-warning" style="font-size: 2rem;"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="col-lg-3 col-md-6">
    <div class="card">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="me-4">
            <span class="round-8 bg-danger rounded-circle d-inline-block"></span>
          </div>
          <div>
            <h4 class="card-title mb-0"><?= formatCurrency($stats['total_revenue']) ?></h4>
            <p class="card-subtitle">Tổng Doanh Thu</p>
          </div>
          <div class="ms-auto">
            <i class="fa fa-money-bill-wave text-danger" style="font-size: 2rem;"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <!-- Đơn hàng gần đây -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">Đơn Hàng Gần Đây</h5>
      </div>
      <div class="card-body">
        <?php if (empty($recent_orders)): ?>
          <p class="text-muted">Chưa Có Đơn Hàng Nào</p>
        <?php else: ?>
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Mã Đơn</th>
                  <th>Khách Hàng</th>
                  <th>Tổng Tiền</th>
                  <th>Trạng Thái</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($recent_orders as $order): ?>
                  <tr>
                    <td><?= htmlspecialchars($order['ma_don_hang']) ?></td>
                    <td><?= htmlspecialchars($order['ho_ten']) ?></td>
                    <td><?= formatCurrency($order['tong_tien']) ?></td>
                    <td>
                      <?php
                      $status_class = [
                          'pending' => 'warning',
                          'completed' => 'success',
                          'cancelled' => 'danger',
                          'refunded' => 'info'
                      ];
                      $status_text = [
                          'pending' => 'Chờ Xử Lý',
                          'completed' => 'Hoàn Thành',
                          'cancelled' => 'Đã Hủy',
                          'refunded' => 'Đã Hoàn Tiền'
                      ];
                      ?>
                      <span class="badge bg-<?= $status_class[$order['trang_thai']] ?>">
                        <?= $status_text[$order['trang_thai']] ?>
                      </span>
                    </td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>
          <div class="text-center">
            <a href="orders.php" class="btn btn-primary btn-sm">
              <i class="fa fa-eye me-1"></i>Xem Tất Cả
            </a>
          </div>
        <?php endif; ?>
      </div>
    </div>
  </div>

  <!-- Khóa học mới -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">Khóa Học Mới</h5>
      </div>
      <div class="card-body">
        <?php if (empty($recent_courses)): ?>
          <p class="text-muted">Chưa Có Khóa Học Nào</p>
        <?php else: ?>
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Tên Khóa Học</th>
                  <th>Danh Mục</th>
                  <th>Giá</th>
                  <th>Trạng Thái</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($recent_courses as $course): ?>
                  <tr>
                    <td><?= htmlspecialchars($course['ten_khoa_hoc']) ?></td>
                    <td><?= htmlspecialchars($course['ten_danh_muc']) ?></td>
                    <td><?= formatCurrency($course['gia_khuyen_mai'] ?: $course['gia_goc']) ?></td>
                    <td>
                      <?php
                      $status_class = [
                          'draft' => 'secondary',
                          'published' => 'success',
                          'archived' => 'warning'
                      ];
                      $status_text = [
                          'draft' => 'Nháp',
                          'published' => 'Đã Xuất Bản',
                          'archived' => 'Lưu Trữ'
                      ];
                      ?>
                      <span class="badge bg-<?= $status_class[$course['trang_thai']] ?>">
                        <?= $status_text[$course['trang_thai']] ?>
                      </span>
                    </td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>
          <div class="text-center">
            <a href="courses.php" class="btn btn-primary btn-sm">
              <i class="fa fa-eye me-1"></i>Xem Tất Cả
            </a>
          </div>
        <?php endif; ?>
      </div>
    </div>
  </div>
</div>

<?php
$content = ob_get_clean();

// Include layout
include '../layouts/admin.php';
?>

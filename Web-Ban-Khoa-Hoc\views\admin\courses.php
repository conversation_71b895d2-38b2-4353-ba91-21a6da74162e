<?php
require_once '../../includes/functions.php';

// Kiểm tra đăng nhập và quyền admin
if (!isLoggedIn() || !isAdmin()) {
    setFlashMessage('error', 'Bạn Không <PERSON>ề<PERSON>ập Trang Này');
    redirect(SITE_URL . '/views/auth/login.php');
}

global $database;

// Xử lý xóa môn học
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    try {
        $course_id = (int)$_GET['id'];
        $database->delete("DELETE FROM courses WHERE id = ?", [$course_id]);
        setFlashMessage('success', 'Xóa Môn Học Thành Công');
        redirect('courses.php');
    } catch (Exception $e) {
        setFlashMessage('error', 'Lỗi: ' . $e->getMessage());
    }
}

// Xử lý thay đổi trạng thái
if (isset($_GET['action']) && $_GET['action'] === 'toggle_status' && isset($_GET['id'])) {
    try {
        $course_id = (int)$_GET['id'];
        $course = $database->selectOne("SELECT trang_thai FROM courses WHERE id = ?", [$course_id]);
        
        if ($course) {
            $new_status = $course['trang_thai'] === 'published' ? 'draft' : 'published';
            $database->update("UPDATE courses SET trang_thai = ? WHERE id = ?", [$new_status, $course_id]);
            setFlashMessage('success', 'Cập Nhật Trạng Thái Thành Công');
        }
        redirect('courses.php');
    } catch (Exception $e) {
        setFlashMessage('error', 'Lỗi: ' . $e->getMessage());
    }
}

// Lấy danh sách môn học với phân trang
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';

$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "c.ten_khoa_hoc LIKE ?";
    $params[] = "%{$search}%";
}

if ($category_filter > 0) {
    $where_conditions[] = "c.category_id = ?";
    $params[] = $category_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "c.trang_thai = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Đếm tổng số môn học
$total_courses = $database->selectOne(
    "SELECT COUNT(*) as total FROM courses c {$where_clause}",
    $params
)['total'];

// Phân trang
$pagination = paginate($total_courses, $page, ADMIN_ITEMS_PER_PAGE);

// Lấy danh sách môn học
$courses = $database->select(
    "SELECT c.*, cat.ten_danh_muc, u.ho_ten as ten_giang_vien 
     FROM courses c 
     LEFT JOIN categories cat ON c.category_id = cat.id 
     LEFT JOIN users u ON c.instructor_id = u.id 
     {$where_clause}
     ORDER BY c.created_at DESC 
     LIMIT {$pagination['items_per_page']} OFFSET {$pagination['offset']}",
    $params
);

// Lấy danh mục cho filter
$categories = $database->select("SELECT * FROM categories ORDER BY ten_danh_muc");

$flash_message = getFlashMessage();
$page_title = 'Quản Lý Môn Học';

// Nội dung trang
ob_start();
?>

<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
          <i class="fa fa-graduation-cap me-2"></i>Quản Lý Môn Học
        </h5>
        <a href="course-add.php" class="btn btn-primary">
          <i class="fa fa-plus me-1"></i>Thêm Môn Học Mới
        </a>
      </div>
      
      <div class="card-body">
        <!-- Bộ lọc -->
        <form method="GET" class="row g-3 mb-4">
          <div class="col-md-4">
            <input type="text" class="form-control" name="search" placeholder="Tìm Kiếm Môn Học..." 
                   value="<?= htmlspecialchars($search) ?>">
          </div>
          <div class="col-md-3">
            <select name="category" class="form-select">
              <option value="">Tất Cả Danh Mục</option>
              <?php foreach ($categories as $category): ?>
                <option value="<?= $category['id'] ?>" <?= $category_filter == $category['id'] ? 'selected' : '' ?>>
                  <?= htmlspecialchars($category['ten_danh_muc']) ?>
                </option>
              <?php endforeach; ?>
            </select>
          </div>
          <div class="col-md-3">
            <select name="status" class="form-select">
              <option value="">Tất Cả Trạng Thái</option>
              <option value="draft" <?= $status_filter === 'draft' ? 'selected' : '' ?>>Nháp</option>
              <option value="published" <?= $status_filter === 'published' ? 'selected' : '' ?>>Đã Xuất Bản</option>
              <option value="archived" <?= $status_filter === 'archived' ? 'selected' : '' ?>>Lưu Trữ</option>
            </select>
          </div>
          <div class="col-md-2">
            <button type="submit" class="btn btn-outline-primary w-100">
              <i class="fa fa-search me-1"></i>Lọc
            </button>
          </div>
        </form>

        <!-- Bảng danh sách -->
        <?php if (empty($courses)): ?>
          <div class="text-center py-5">
            <i class="fa fa-graduation-cap text-muted" style="font-size: 4rem;"></i>
            <h5 class="mt-3 text-muted">Chưa Có Môn Học Nào</h5>
            <p class="text-muted">Hãy Thêm Môn Học Đầu Tiên</p>
            <a href="course-add.php" class="btn btn-primary">
              <i class="fa fa-plus me-1"></i>Thêm Môn Học
            </a>
          </div>
        <?php else: ?>
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th>Hình Ảnh</th>
                  <th>Tên Môn Học</th>
                  <th>Danh Mục</th>
                  <th>Giảng Viên</th>
                  <th>Giá</th>
                  <th>Trạng Thái</th>
                  <th>Học Viên</th>
                  <th>Thao Tác</th>
                </tr>
              </thead>
              <tbody id="courses-table">
                <?php foreach ($courses as $course): ?>
                  <tr data-course-id="<?= $course['id'] ?>">
                    <td>
                      <img src="<?= UPLOADS_URL ?>/courses/<?= $course['hinh_anh'] ?: 'default.jpg' ?>" 
                           alt="<?= htmlspecialchars($course['ten_khoa_hoc']) ?>" 
                           class="rounded" width="60" height="40" style="object-fit: cover;">
                    </td>
                    <td>
                      <div>
                        <h6 class="mb-1"><?= htmlspecialchars($course['ten_khoa_hoc']) ?></h6>
                        <small class="text-muted">
                          <i class="fa fa-clock me-1"></i><?= $course['thoi_gian_hoc'] ?> giờ
                          <?php if ($course['noi_bat']): ?>
                            <span class="badge bg-warning ms-2">Nổi Bật</span>
                          <?php endif; ?>
                        </small>
                      </div>
                    </td>
                    <td><?= htmlspecialchars($course['ten_danh_muc'] ?: 'Chưa Phân Loại') ?></td>
                    <td><?= htmlspecialchars($course['ten_giang_vien'] ?: 'Chưa Gán') ?></td>
                    <td>
                      <?php if ($course['gia_khuyen_mai']): ?>
                        <div>
                          <span class="text-decoration-line-through text-muted small">
                            <?= formatCurrency($course['gia_goc']) ?>
                          </span>
                          <br>
                          <span class="fw-bold text-primary">
                            <?= formatCurrency($course['gia_khuyen_mai']) ?>
                          </span>
                        </div>
                      <?php else: ?>
                        <span class="fw-bold"><?= formatCurrency($course['gia_goc']) ?></span>
                      <?php endif; ?>
                    </td>
                    <td>
                      <?php
                      $status_class = [
                          'draft' => 'secondary',
                          'published' => 'success',
                          'archived' => 'warning'
                      ];
                      $status_text = [
                          'draft' => 'Nháp',
                          'published' => 'Đã Xuất Bản',
                          'archived' => 'Lưu Trữ'
                      ];
                      ?>
                      <span class="badge bg-<?= $status_class[$course['trang_thai']] ?>">
                        <?= $status_text[$course['trang_thai']] ?>
                      </span>
                    </td>
                    <td>
                      <span class="badge bg-info"><?= number_format($course['so_hoc_vien']) ?></span>
                    </td>
                    <td>
                      <div class="btn-group" role="group">
                        <a href="course-edit.php?id=<?= $course['id'] ?>" 
                           class="btn btn-sm btn-outline-primary" title="Chỉnh Sửa">
                          <i class="fa fa-edit"></i>
                        </a>
                        <a href="course-chapters.php?course_id=<?= $course['id'] ?>" 
                           class="btn btn-sm btn-outline-info" title="Quản Lý Chương">
                          <i class="fa fa-list"></i>
                        </a>
                        <a href="?action=toggle_status&id=<?= $course['id'] ?>" 
                           class="btn btn-sm btn-outline-<?= $course['trang_thai'] === 'published' ? 'warning' : 'success' ?>" 
                           title="<?= $course['trang_thai'] === 'published' ? 'Ẩn' : 'Xuất Bản' ?>">
                          <i class="fa fa-<?= $course['trang_thai'] === 'published' ? 'eye-slash' : 'eye' ?>"></i>
                        </a>
                        <a href="?action=delete&id=<?= $course['id'] ?>" 
                           class="btn btn-sm btn-outline-danger" title="Xóa"
                           onclick="return confirm('Bạn Có Chắc Muốn Xóa Môn Học Này?')">
                          <i class="fa fa-trash"></i>
                        </a>
                      </div>
                    </td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>

          <!-- Phân trang -->
          <?php if ($pagination['total_pages'] > 1): ?>
            <nav aria-label="Phân trang">
              <ul class="pagination justify-content-center">
                <?php if ($pagination['has_prev']): ?>
                  <li class="page-item">
                    <a class="page-link" href="?page=<?= $pagination['prev_page'] ?>&search=<?= urlencode($search) ?>&category=<?= $category_filter ?>&status=<?= urlencode($status_filter) ?>">
                      <i class="fa fa-chevron-left"></i>
                    </a>
                  </li>
                <?php endif; ?>

                <?php for ($i = 1; $i <= $pagination['total_pages']; $i++): ?>
                  <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                    <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&category=<?= $category_filter ?>&status=<?= urlencode($status_filter) ?>">
                      <?= $i ?>
                    </a>
                  </li>
                <?php endfor; ?>

                <?php if ($pagination['has_next']): ?>
                  <li class="page-item">
                    <a class="page-link" href="?page=<?= $pagination['next_page'] ?>&search=<?= urlencode($search) ?>&category=<?= $category_filter ?>&status=<?= urlencode($status_filter) ?>">
                      <i class="fa fa-chevron-right"></i>
                    </a>
                  </li>
                <?php endif; ?>
              </ul>
            </nav>
          <?php endif; ?>
        <?php endif; ?>
      </div>
    </div>
  </div>
</div>

<?php
$content = ob_get_clean();

// JavaScript cho drag & drop sắp xếp
$additional_js = '
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
// Drag & Drop sắp xếp thứ tự môn học
if (document.getElementById("courses-table")) {
    new Sortable(document.getElementById("courses-table"), {
        animation: 150,
        ghostClass: "sortable-ghost",
        onEnd: function(evt) {
            // Gửi AJAX để cập nhật thứ tự
            const courseIds = Array.from(document.querySelectorAll("[data-course-id]"))
                .map(row => row.getAttribute("data-course-id"));
            
            fetch("course-reorder.php", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({course_ids: courseIds})
            });
        }
    });
}
</script>
<style>
.sortable-ghost {
    opacity: 0.4;
}
</style>
';

// Include layout
include '../layouts/admin.php';
?>

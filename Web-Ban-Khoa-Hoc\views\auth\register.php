<?php
require_once '../../includes/functions.php';

// Xử lý đăng ký
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $ho_ten = sanitize($_POST['ho_ten'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $so_dien_thoai = sanitize($_POST['so_dien_thoai'] ?? '');
        $agree_terms = isset($_POST['agree_terms']);
        
        // Validate
        if (empty($ho_ten) || empty($email) || empty($password) || empty($confirm_password)) {
            throw new Exception('Vui Lòng Nhập Đầy Đủ Thông Tin');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('<PERSON><PERSON>');
        }
        
        if (strlen($password) < 6) {
            throw new Exception('Mật <PERSON>h<PERSON>u <PERSON>ải <PERSON>ó <PERSON>t <PERSON>ất 6 Ký Tự');
        }
        
        if ($password !== $confirm_password) {
            throw new Exception('Mật Khẩu Xác Nhận Không Khớp');
        }
        
        if (!$agree_terms) {
            throw new Exception('Vui Lòng Đồng Ý Với Điều Khoản Sử Dụng');
        }
        
        // Kiểm tra email đã tồn tại
        global $database;
        if ($database->exists('users', 'email = ?', [$email])) {
            throw new Exception('Email Này Đã Được Sử Dụng');
        }
        
        // Tạo tài khoản mới
        $user_id = $database->insert(
            "INSERT INTO users (ho_ten, email, mat_khau, so_dien_thoai, vai_tro, trang_thai, created_at) 
             VALUES (?, ?, ?, ?, 'student', 'active', NOW())",
            [$ho_ten, $email, hashPassword($password), $so_dien_thoai]
        );
        
        if ($user_id) {
            // Đăng ký thành công, tự động đăng nhập
            $_SESSION['user_id'] = $user_id;
            $_SESSION['user_name'] = $ho_ten;
            $_SESSION['user_email'] = $email;
            $_SESSION['user_role'] = 'student';
            
            setFlashMessage('success', 'Đăng Ký Thành Công! Chào Mừng Bạn Đến Với ' . SITE_NAME);
            redirect(SITE_URL . '/views/user/dashboard.php');
        } else {
            throw new Exception('Có Lỗi Xảy Ra Khi Tạo Tài Khoản');
        }
        
    } catch (Exception $e) {
        $flash_message = ['type' => 'error', 'message' => $e->getMessage()];
    }
}

// Lấy flash message từ session
if (!isset($flash_message)) {
    $flash_message = getFlashMessage();
}

$page_title = 'Đăng Ký';

// Nội dung trang
ob_start();
?>

<h2 class="lh-base mb-4">Hãy Tạo Tài Khoản Của Bạn</h2>

<div class="row">
  <div class="col-6 mb-2 mb-sm-0">
    <a class="btn btn-white shadow-sm text-dark link-primary border fw-semibold d-flex align-items-center justify-content-center rounded-1 py-6" href="javascript:void(0)" role="button">
      <i class="fab fa-facebook-f text-primary me-2"></i>
      <span class="d-none d-sm-inline-flex">Đăng Ký Với</span>
      <span class="d-sm-none">Facebook</span>
    </a>
  </div>
  <div class="col-6">
    <a class="btn btn-white shadow-sm text-dark link-primary border fw-semibold d-flex align-items-center justify-content-center rounded-1 py-6" href="javascript:void(0)" role="button">
      <i class="fab fa-google text-danger me-2"></i>
      <span class="d-none d-sm-inline-flex">Đăng Ký Với</span>
      <span class="d-sm-none">Google</span>
    </a>
  </div>
</div>

<div class="position-relative text-center my-4">
  <p class="mb-0 fs-12 px-3 d-inline-block bg-body z-index-5 position-relative">Hoặc Đăng Ký Với Email</p>
  <span class="border-top w-100 position-absolute top-50 start-50 translate-middle"></span>
</div>

<form method="POST" action="">
  <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">

  <div class="mb-3">
    <label for="ho_ten" class="form-label">Họ Và Tên</label>
    <input type="text" class="form-control" id="ho_ten" name="ho_ten" placeholder="Nhập Họ Và Tên"
           value="<?= htmlspecialchars($_POST['ho_ten'] ?? '') ?>" aria-describedby="nameHelp" required>
  </div>

  <div class="mb-3">
    <label for="email" class="form-label">Địa Chỉ Email</label>
    <input type="email" class="form-control" id="email" name="email" placeholder="Nhập Email Của Bạn"
           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" aria-describedby="emailHelp" required>
  </div>

  <div class="mb-3">
    <label for="so_dien_thoai" class="form-label">Số Điện Thoại</label>
    <input type="tel" class="form-control" id="so_dien_thoai" name="so_dien_thoai" placeholder="Nhập Số Điện Thoại"
           value="<?= htmlspecialchars($_POST['so_dien_thoai'] ?? '') ?>">
  </div>

  <div class="mb-3">
    <label for="password" class="form-label">Mật Khẩu</label>
    <input type="password" class="form-control" id="password" name="password" placeholder="Nhập Mật Khẩu (Ít Nhất 6 Ký Tự)" required>
  </div>

  <div class="mb-4">
    <label for="confirm_password" class="form-label">Xác Nhận Mật Khẩu</label>
    <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Nhập Lại Mật Khẩu" required>
  </div>

  <div class="d-flex align-items-center justify-content-between mb-4">
    <div class="form-check">
      <input class="form-check-input primary" type="checkbox" value="" id="agree_terms" name="agree_terms"
             <?= isset($_POST['agree_terms']) ? 'checked' : '' ?> required>
      <label class="form-check-label text-dark" for="agree_terms">
        Tôi Đồng Ý Với <a href="#" class="text-primary">Điều Khoản Sử Dụng</a>
      </label>
    </div>
  </div>

  <button type="submit" class="btn btn-dark w-100 py-8 mb-4 rounded-1">Đăng Ký</button>

  <div class="d-flex align-items-center">
    <p class="fs-12 mb-0 fw-medium">Đã Có Tài Khoản?</p>
    <a class="text-primary fw-bolder ms-2" href="login.php">Đăng Nhập Ngay</a>
  </div>
</form>

<?php
$content = ob_get_clean();

// Include layout
include '../layouts/auth.php';
?>

<script>
// Kiểm tra mật khẩu khớp
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Mật Khẩu Xác Nhận Không Khớp');
    } else {
        this.setCustomValidity('');
    }
});
</script>

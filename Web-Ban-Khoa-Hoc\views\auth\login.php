<?php
require_once '../../includes/functions.php';

// Xử lý đăng nhập
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $email = sanitize($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $remember = isset($_POST['remember']);
        
        // Validate
        if (empty($email) || empty($password)) {
            throw new Exception('Vui Lòng Nhập Đầy Đủ Thông Tin');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Email Không Hợp Lệ');
        }
        
        // Ki<PERSON>m tra user trong database
        global $database;
        $user = $database->selectOne(
            "SELECT * FROM users WHERE email = ? AND trang_thai = 'active'",
            [$email]
        );
        
        if (!$user || !verifyPassword($password, $user['mat_khau'])) {
            throw new Exception('Email Hoặc M<PERSON>t <PERSON>u <PERSON>hông Chính Xác');
        }
        
        // Đăng nhập thành công
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['ho_ten'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = $user['vai_tro'];
        
        // Remember me
        if ($remember) {
            $token = generateToken();
            $database->update(
                "UPDATE users SET remember_token = ? WHERE id = ?",
                [$token, $user['id']]
            );
            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 ngày
        }
        
        // Chuyển hướng
        if ($user['vai_tro'] === 'admin') {
            redirect(SITE_URL . '/views/admin/dashboard.php');
        } else {
            redirect(SITE_URL . '/views/user/dashboard.php');
        }
        
    } catch (Exception $e) {
        $flash_message = ['type' => 'error', 'message' => $e->getMessage()];
    }
}

// Lấy flash message từ session
if (!isset($flash_message)) {
    $flash_message = getFlashMessage();
}

$page_title = 'Đăng Nhập';

// Nội dung trang
ob_start();
?>

<h2 class="lh-base mb-4">Chào Mừng Bạn Quay Trở Lại</h2>

<div class="row">
  <div class="col-6 mb-2 mb-sm-0">
    <a class="btn btn-white shadow-sm text-dark link-primary border fw-semibold d-flex align-items-center justify-content-center rounded-1 py-6" href="javascript:void(0)" role="button">
      <img src="<?= ASSETS_URL ?>/images/svgs/facebook-icon.svg" alt="matdash-img" class="img-fluid me-2" width="18" height="18">
      <span class="d-none d-xxl-inline-flex"> Đăng Nhập Với </span>&nbsp; Facebook
    </a>
  </div>
  <div class="col-6">
    <a class="btn btn-white shadow-sm text-dark link-primary border fw-semibold d-flex align-items-center justify-content-center rounded-1 py-6" href="javascript:void(0)" role="button">
      <img src="<?= ASSETS_URL ?>/images/svgs/google-icon.svg" alt="matdash-img" class="img-fluid me-2" width="18" height="18">
      <span class="d-none d-xxl-inline-flex"> Đăng Nhập Với </span>&nbsp; Google
    </a>
  </div>
</div>

<div class="position-relative text-center my-4">
  <p class="mb-0 fs-12 px-3 d-inline-block bg-body z-index-5 position-relative">Hoặc Đăng Nhập Với Email</p>
  <span class="border-top w-100 position-absolute top-50 start-50 translate-middle"></span>
</div>

<form method="POST" action="">
  <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">

  <div class="mb-3">
    <label for="email" class="form-label">Địa Chỉ Email</label>
    <input type="email" class="form-control" id="email" name="email" placeholder="Nhập Email Của Bạn"
           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" aria-describedby="emailHelp" required>
  </div>

  <div class="mb-4">
    <div class="d-flex align-items-center justify-content-between">
      <label for="password" class="form-label">Mật Khẩu</label>
      <a class="text-primary link-dark fs-2" href="forgot-password.php">Quên Mật Khẩu?</a>
    </div>
    <input type="password" class="form-control" id="password" name="password" placeholder="Nhập Mật Khẩu" required>
  </div>

  <div class="d-flex align-items-center justify-content-between mb-4">
    <div class="form-check">
      <input class="form-check-input primary" type="checkbox" value="" id="remember" name="remember"
             <?= isset($_POST['remember']) ? 'checked' : '' ?>>
      <label class="form-check-label text-dark" for="remember">
        Ghi Nhớ Đăng Nhập
      </label>
    </div>
  </div>

  <button type="submit" class="btn btn-dark w-100 py-8 mb-4 rounded-1">Đăng Nhập</button>

  <div class="d-flex align-items-center">
    <p class="fs-12 mb-0 fw-medium">Chưa Có Tài Khoản?</p>
    <a class="text-primary fw-bolder ms-2" href="register.php">Đăng Ký Ngay</a>
  </div>
</form>

<?php
$content = ob_get_clean();

// Include layout
include '../layouts/auth.php';
?>

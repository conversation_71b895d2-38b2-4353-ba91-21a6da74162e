<?php
require_once '../../includes/functions.php';

// Kiểm tra đăng nhập và quyền admin
if (!isLoggedIn() || !isAdmin()) {
    setFlashMessage('error', 'Bạn Không <PERSON>ó <PERSON>ề<PERSON>ập Trang <PERSON>à<PERSON>');
    redirect(SITE_URL . '/views/auth/login.php');
}

$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
if (!$course_id) {
    setFlashMessage('error', '<PERSON>ôn <PERSON>ông Tồn Tại');
    redirect('courses.php');
}

global $database;

// Lấy thông tin môn học
$course = $database->selectOne("SELECT * FROM courses WHERE id = ?", [$course_id]);
if (!$course) {
    setFlashMessage('error', '<PERSON><PERSON><PERSON>ng Tồn Tại');
    redirect('courses.php');
}

// Xử lý thêm chương mới
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_chapter') {
    try {
        if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Token Bảo Mật Không Hợp Lệ');
        }

        $ten_chuong = sanitize($_POST['ten_chuong'] ?? '');
        $mo_ta = sanitize($_POST['mo_ta'] ?? '');

        if (empty($ten_chuong)) {
            throw new Exception('Vui Lòng Nhập Tên Chương');
        }

        // Lấy thứ tự tiếp theo
        $max_order = $database->selectOne(
            "SELECT MAX(thu_tu) as max_order FROM course_chapters WHERE course_id = ?",
            [$course_id]
        )['max_order'] ?? 0;

        $database->insert(
            "INSERT INTO course_chapters (course_id, ten_chuong, mo_ta, thu_tu, created_at) 
             VALUES (?, ?, ?, ?, datetime('now'))",
            [$course_id, $ten_chuong, $mo_ta, $max_order + 1]
        );

        setFlashMessage('success', 'Thêm Chương Thành Công');
        redirect("course-chapters.php?course_id={$course_id}");

    } catch (Exception $e) {
        $flash_message = ['type' => 'error', 'message' => $e->getMessage()];
    }
}

// Xử lý xóa chương
if (isset($_GET['action']) && $_GET['action'] === 'delete_chapter' && isset($_GET['chapter_id'])) {
    try {
        $chapter_id = (int)$_GET['chapter_id'];
        
        // Kiểm tra chương có bài học không
        $lesson_count = $database->count('course_lessons', 'chapter_id = ?', [$chapter_id]);
        if ($lesson_count > 0) {
            throw new Exception('Không Thể Xóa Chương Có Bài Học. Vui Lòng Xóa Tất Cả Bài Học Trước.');
        }

        $database->delete("DELETE FROM course_chapters WHERE id = ? AND course_id = ?", [$chapter_id, $course_id]);
        setFlashMessage('success', 'Xóa Chương Thành Công');
        redirect("course-chapters.php?course_id={$course_id}");

    } catch (Exception $e) {
        setFlashMessage('error', 'Lỗi: ' . $e->getMessage());
        redirect("course-chapters.php?course_id={$course_id}");
    }
}

// Lấy danh sách chương với số bài học
$chapters = $database->select(
    "SELECT c.*, COUNT(l.id) as so_bai_hoc 
     FROM course_chapters c 
     LEFT JOIN course_lessons l ON c.id = l.chapter_id 
     WHERE c.course_id = ? 
     GROUP BY c.id 
     ORDER BY c.thu_tu ASC",
    [$course_id]
);

$flash_message = $flash_message ?? getFlashMessage();
$page_title = 'Quản Lý Chương: ' . $course['ten_khoa_hoc'];

// Nội dung trang
ob_start();
?>

<div class="row">
  <div class="col-12">
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fa fa-graduation-cap me-2"></i><?= htmlspecialchars($course['ten_khoa_hoc']) ?>
        </h5>
        <p class="mb-0 text-muted">Quản Lý Chương Học</p>
      </div>
    </div>

    <div class="row">
      <!-- Form thêm chương -->
      <div class="col-lg-4">
        <div class="card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="fa fa-plus me-2"></i>Thêm Chương Mới
            </h6>
          </div>
          
          <form method="POST">
            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
            <input type="hidden" name="action" value="add_chapter">
            
            <div class="card-body">
              <div class="mb-3">
                <label for="ten_chuong" class="form-label">Tên Chương <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="ten_chuong" name="ten_chuong" 
                       placeholder="Nhập tên chương..." required>
              </div>

              <div class="mb-3">
                <label for="mo_ta" class="form-label">Mô Tả</label>
                <textarea class="form-control" id="mo_ta" name="mo_ta" rows="3" 
                          placeholder="Mô tả ngắn về chương..."></textarea>
              </div>
            </div>

            <div class="card-footer">
              <button type="submit" class="btn btn-primary w-100">
                <i class="fa fa-plus me-1"></i>Thêm Chương
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Danh sách chương -->
      <div class="col-lg-8">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="card-title mb-0">
              <i class="fa fa-list me-2"></i>Danh Sách Chương (<?= count($chapters) ?>)
            </h6>
            <a href="course-edit.php?id=<?= $course_id ?>" class="btn btn-secondary btn-sm">
              <i class="fa fa-arrow-left me-1"></i>Quay Lại Môn Học
            </a>
          </div>
          
          <div class="card-body">
            <?php if (empty($chapters)): ?>
              <div class="text-center py-5">
                <i class="fa fa-list text-muted" style="font-size: 3rem;"></i>
                <h6 class="mt-3 text-muted">Chưa Có Chương Nào</h6>
                <p class="text-muted">Hãy Thêm Chương Đầu Tiên Cho Môn Học</p>
              </div>
            <?php else: ?>
              <div id="chapters-list">
                <?php foreach ($chapters as $index => $chapter): ?>
                  <div class="chapter-item border rounded p-3 mb-3" data-chapter-id="<?= $chapter['id'] ?>">
                    <div class="d-flex justify-content-between align-items-start">
                      <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                          <span class="badge bg-primary me-2">Chương <?= $index + 1 ?></span>
                          <h6 class="mb-0"><?= htmlspecialchars($chapter['ten_chuong']) ?></h6>
                          <i class="fa fa-grip-vertical ms-2 text-muted" style="cursor: move;" title="Kéo để sắp xếp"></i>
                        </div>
                        
                        <?php if ($chapter['mo_ta']): ?>
                          <p class="text-muted mb-2"><?= htmlspecialchars($chapter['mo_ta']) ?></p>
                        <?php endif; ?>
                        
                        <div class="d-flex align-items-center text-muted small">
                          <i class="fa fa-book me-1"></i>
                          <span><?= $chapter['so_bai_hoc'] ?> bài học</span>
                          <span class="mx-2">•</span>
                          <i class="fa fa-calendar me-1"></i>
                          <span><?= formatDate($chapter['created_at'], 'd/m/Y') ?></span>
                        </div>
                      </div>
                      
                      <div class="btn-group" role="group">
                        <a href="chapter-lessons.php?chapter_id=<?= $chapter['id'] ?>&course_id=<?= $course_id ?>" 
                           class="btn btn-sm btn-outline-info" title="Quản Lý Bài Học">
                          <i class="fa fa-play"></i>
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                onclick="editChapter(<?= $chapter['id'] ?>, '<?= htmlspecialchars($chapter['ten_chuong'], ENT_QUOTES) ?>', '<?= htmlspecialchars($chapter['mo_ta'], ENT_QUOTES) ?>')" 
                                title="Chỉnh Sửa">
                          <i class="fa fa-edit"></i>
                        </button>
                        <a href="?course_id=<?= $course_id ?>&action=delete_chapter&chapter_id=<?= $chapter['id'] ?>" 
                           class="btn btn-sm btn-outline-danger" title="Xóa"
                           onclick="return confirm('Bạn có chắc muốn xóa chương này?\n\nLưu ý: Chỉ có thể xóa chương không có bài học.')">
                          <i class="fa fa-trash"></i>
                        </a>
                      </div>
                    </div>
                  </div>
                <?php endforeach; ?>
              </div>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal chỉnh sửa chương -->
<div class="modal fade" id="editChapterModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <form id="editChapterForm" method="POST">
        <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
        <input type="hidden" name="action" value="edit_chapter">
        <input type="hidden" name="chapter_id" id="edit_chapter_id">
        
        <div class="modal-header">
          <h5 class="modal-title">Chỉnh Sửa Chương</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        
        <div class="modal-body">
          <div class="mb-3">
            <label for="edit_ten_chuong" class="form-label">Tên Chương <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="edit_ten_chuong" name="ten_chuong" required>
          </div>
          
          <div class="mb-3">
            <label for="edit_mo_ta" class="form-label">Mô Tả</label>
            <textarea class="form-control" id="edit_mo_ta" name="mo_ta" rows="3"></textarea>
          </div>
        </div>
        
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-primary">Cập Nhật</button>
        </div>
      </form>
    </div>
  </div>
</div>

<?php
$content = ob_get_clean();

// JavaScript
$additional_js = '
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
// Drag & Drop sắp xếp chương
if (document.getElementById("chapters-list")) {
    new Sortable(document.getElementById("chapters-list"), {
        handle: ".fa-grip-vertical",
        animation: 150,
        ghostClass: "sortable-ghost",
        onEnd: function(evt) {
            const chapterIds = Array.from(document.querySelectorAll("[data-chapter-id]"))
                .map(item => item.getAttribute("data-chapter-id"));
            
            fetch("chapter-reorder.php", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    course_id: ' . $course_id . ',
                    chapter_ids: chapterIds
                })
            });
        }
    });
}

// Chỉnh sửa chương
function editChapter(id, name, description) {
    document.getElementById("edit_chapter_id").value = id;
    document.getElementById("edit_ten_chuong").value = name;
    document.getElementById("edit_mo_ta").value = description;
    
    new bootstrap.Modal(document.getElementById("editChapterModal")).show();
}
</script>
<style>
.chapter-item {
    transition: all 0.3s ease;
}
.chapter-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.sortable-ghost {
    opacity: 0.4;
}
</style>
';

// Include layout
include '../layouts/admin.php';
?>

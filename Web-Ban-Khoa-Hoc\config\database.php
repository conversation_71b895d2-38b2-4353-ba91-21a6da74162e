<?php
require_once 'config.php';

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    private $pdo;

    /**
     * Kết <PERSON><PERSON> Sở Dữ Liệu
     */
    public function connect() {
        if ($this->pdo === null) {
            try {
                $dsn = "mysql:host={$this->host};dbname={$this->db_name};charset={$this->charset}";
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ];
                
                $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
            } catch (PDOException $e) {
                throw new Exception("Lỗi Kết N<PERSON> Sở Dữ Liệu: " . $e->getMessage());
            }
        }
        
        return $this->pdo;
    }

    /**
     * Thực Hiện Truy Vấn SELECT
     */
    public function select($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new Exception("Lỗi Truy Vấn SELECT: " . $e->getMessage());
        }
    }

    /**
     * Thực Hiện Truy Vấn SELECT Một Bản Ghi
     */
    public function selectOne($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            throw new Exception("Lỗi Truy Vấn SELECT ONE: " . $e->getMessage());
        }
    }

    /**
     * Thực Hiện Truy Vấn INSERT
     */
    public function insert($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $this->connect()->lastInsertId();
        } catch (PDOException $e) {
            throw new Exception("Lỗi Truy Vấn INSERT: " . $e->getMessage());
        }
    }

    /**
     * Thực Hiện Truy Vấn UPDATE
     */
    public function update($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new Exception("Lỗi Truy Vấn UPDATE: " . $e->getMessage());
        }
    }

    /**
     * Thực Hiện Truy Vấn DELETE
     */
    public function delete($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new Exception("Lỗi Truy Vấn DELETE: " . $e->getMessage());
        }
    }

    /**
     * Bắt Đầu Transaction
     */
    public function beginTransaction() {
        return $this->connect()->beginTransaction();
    }

    /**
     * Commit Transaction
     */
    public function commit() {
        return $this->connect()->commit();
    }

    /**
     * Rollback Transaction
     */
    public function rollback() {
        return $this->connect()->rollback();
    }

    /**
     * Đếm Số Bản Ghi
     */
    public function count($table, $where = '', $params = []) {
        $sql = "SELECT COUNT(*) as total FROM {$table}";
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        $result = $this->selectOne($sql, $params);
        return $result['total'] ?? 0;
    }

    /**
     * Kiểm Tra Bản Ghi Tồn Tại
     */
    public function exists($table, $where, $params = []) {
        return $this->count($table, $where, $params) > 0;
    }
}

// Tạo Instance Global
$database = new Database();
?>

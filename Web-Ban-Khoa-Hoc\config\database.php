<?php
require_once 'config.php';

class Database {
    private $pdo;

    /**
     * Kết <PERSON><PERSON>i <PERSON> Sở Dữ Liệu SQLite
     */
    public function connect() {
        if ($this->pdo === null) {
            try {
                $dsn = "sqlite:" . DB_PATH;
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ];

                $this->pdo = new PDO($dsn, null, null, $options);

                // Bật foreign key constraints cho SQLite
                $this->pdo->exec("PRAGMA foreign_keys = ON");

            } catch (PDOException $e) {
                throw new Exception("Lỗi Kết Nối Cơ Sở Dữ Liệu: " . $e->getMessage());
            }
        }

        return $this->pdo;
    }

    /**
     * Thực Hiện Truy Vấn SELECT
     */
    public function select($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new Exception("Lỗi Truy Vấn SELECT: " . $e->getMessage());
        }
    }

    /**
     * Thực Hiện Truy Vấn SELECT Một Bản Ghi
     */
    public function selectOne($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            throw new Exception("Lỗi Truy Vấn SELECT ONE: " . $e->getMessage());
        }
    }

    /**
     * Thực Hiện Truy Vấn INSERT
     */
    public function insert($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $this->connect()->lastInsertId();
        } catch (PDOException $e) {
            throw new Exception("Lỗi Truy Vấn INSERT: " . $e->getMessage());
        }
    }

    /**
     * Thực Hiện Truy Vấn UPDATE
     */
    public function update($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new Exception("Lỗi Truy Vấn UPDATE: " . $e->getMessage());
        }
    }

    /**
     * Thực Hiện Truy Vấn DELETE
     */
    public function delete($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new Exception("Lỗi Truy Vấn DELETE: " . $e->getMessage());
        }
    }

    /**
     * Bắt Đầu Transaction
     */
    public function beginTransaction() {
        return $this->connect()->beginTransaction();
    }

    /**
     * Commit Transaction
     */
    public function commit() {
        return $this->connect()->commit();
    }

    /**
     * Rollback Transaction
     */
    public function rollback() {
        return $this->connect()->rollback();
    }

    /**
     * Đếm Số Bản Ghi
     */
    public function count($table, $where = '', $params = []) {
        $sql = "SELECT COUNT(*) as total FROM {$table}";
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        $result = $this->selectOne($sql, $params);
        return $result['total'] ?? 0;
    }

    /**
     * Kiểm Tra Bản Ghi Tồn Tại
     */
    public function exists($table, $where, $params = []) {
        return $this->count($table, $where, $params) > 0;
    }
}

// Tạo Instance Global
$database = new Database();
?>

<?php
require_once '../../includes/functions.php';

// <PERSON><PERSON> lý quên mật khẩu
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $email = sanitize($_POST['email'] ?? '');
        
        // Validate
        if (empty($email)) {
            throw new Exception('Vui Lòng Nhập Email');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Email Không Hợp Lệ');
        }
        
        // Kiểm tra email có tồn tại
        global $database;
        $user = $database->selectOne(
            "SELECT * FROM users WHERE email = ? AND trang_thai = 'active'",
            [$email]
        );
        
        if (!$user) {
            throw new Exception('Email Này Không Tồn Tại Trong Hệ Thống');
        }
        
        // Tạo reset token
        $reset_token = generateToken();
        $reset_expires = date('Y-m-d H:i:s', time() + 3600); // 1 giờ
        
        // Cập nhật token vào database
        $database->update(
            "UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE id = ?",
            [$reset_token, $reset_expires, $user['id']]
        );
        
        // Gửi email (tạm thời chỉ hiển thị link)
        $reset_link = SITE_URL . "/views/auth/reset-password.php?token=" . $reset_token;
        
        $flash_message = [
            'type' => 'success', 
            'message' => 'Link Đặt Lại Mật Khẩu Đã Được Gửi Đến Email Của Bạn. Vui Lòng Kiểm Tra Hộp Thư.'
        ];
        
        // Trong môi trường development, hiển thị link trực tiếp
        if (defined('DEVELOPMENT') && DEVELOPMENT === true) {
            $flash_message['message'] .= '<br><strong>Link Reset (Development):</strong> <a href="' . $reset_link . '">' . $reset_link . '</a>';
        }
        
    } catch (Exception $e) {
        $flash_message = ['type' => 'error', 'message' => $e->getMessage()];
    }
}

// Lấy flash message từ session
if (!isset($flash_message)) {
    $flash_message = getFlashMessage();
}

$page_title = 'Quên Mật Khẩu';

// Nội dung trang
ob_start();
?>

<h2 class="lh-base mb-4">Quên Mật Khẩu?</h2>
<p class="mb-4 text-muted">Nhập Email Của Bạn Và Chúng Tôi Sẽ Gửi Link Đặt Lại Mật Khẩu</p>

<form method="POST" action="">
    <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
    
    <div class="mb-4">
        <label for="email" class="form-label">Địa Chỉ Email</label>
        <input type="email" class="form-control" id="email" name="email" 
               placeholder="Nhập Email Của Bạn" value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required>
    </div>
    
    <button type="submit" class="btn btn-dark w-100 py-8 mb-4 rounded-1">
        <i class="fa fa-paper-plane me-2"></i>Gửi Link Đặt Lại
    </button>
    
    <div class="d-flex align-items-center justify-content-center">
        <a class="text-primary fw-bolder" href="login.php">
            <i class="fa fa-arrow-left me-2"></i>Quay Lại Đăng Nhập
        </a>
    </div>
</form>

<?php
$content = ob_get_clean();

// Include layout
include '../layouts/auth.php';
?>

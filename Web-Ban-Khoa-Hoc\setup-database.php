<?php
require_once 'config/config.php';

echo "<h1><PERSON>hi<PERSON><PERSON>p <PERSON> Sở Dữ Liệu SQLite - " . SITE_NAME . "</h1>";

try {
    // Tạo thư mục database nếu chưa có
    $db_dir = dirname(DB_PATH);
    if (!is_dir($db_dir)) {
        mkdir($db_dir, 0755, true);
        echo "<p>✅ Tạo thư mục database thành công!</p>";
    }

    // Kết nối SQLite
    $pdo = new PDO("sqlite:" . DB_PATH);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->exec("PRAGMA foreign_keys = ON");

    echo "<p>✅ Kết nối SQLite thành công!</p>";
    echo "<p>📍 Đường dẫn database: " . DB_PATH . "</p>";

    // Đọc file SQL
    $sql_file = 'database/sqlite_schema.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("File sqlite_schema.sql không tồn tại!");
    }

    $sql_content = file_get_contents($sql_file);

    echo "<p>📄 Đọc file sqlite_schema.sql thành công!</p>";
    echo "<p>🔄 Đang thực thi các câu lệnh SQL...</p>";

    // Thực thi toàn bộ SQL
    $pdo->exec($sql_content);

    echo "<p style='color: green;'>✅ Tạo bảng và dữ liệu mẫu thành công!</p>";

    // Kiểm tra dữ liệu
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE vai_tro = 'admin'");
    $admin_count = $stmt->fetch()['count'];

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
    $category_count = $stmt->fetch()['count'];

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM courses");
    $course_count = $stmt->fetch()['count'];

    echo "<h2>📊 Thống Kê Dữ Liệu</h2>";
    echo "<ul>";
    echo "<li>👤 Admin: {$admin_count} tài khoản</li>";
    echo "<li>📂 Danh mục: {$category_count} danh mục</li>";
    echo "<li>🎓 Khóa học: {$course_count} khóa học</li>";
    echo "</ul>";

    echo "<h2>🎉 Thiết Lập Hoàn Tất!</h2>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>🔑 Thông tin đăng nhập Admin mặc định:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Email:</strong> <EMAIL></li>";
    echo "<li><strong>Mật khẩu:</strong> password</li>";
    echo "</ul>";
    echo "</div>";

    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='index.php' style='background: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; margin: 0 10px; display: inline-block;'>🏠 Về Trang Chủ</a>";
    echo "<a href='views/auth/login.php' style='background: #28a745; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; margin: 0 10px; display: inline-block;'>🔑 Đăng Nhập Admin</a>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p style='color: red;'>❌ <strong>Lỗi:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";

    echo "<h3>🔧 Hướng Dẫn Khắc Phục:</h3>";
    echo "<ol>";
    echo "<li>Đảm bảo PHP đã được cài đặt và có extension SQLite</li>";
    echo "<li>Kiểm tra quyền ghi file trong thư mục dự án</li>";
    echo "<li>Đảm bảo file sqlite_schema.sql tồn tại trong thư mục database/</li>";
    echo "<li>Thử chạy lại setup-database.php</li>";
    echo "</ol>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background-color: #f8f9fa;
}
h1 {
    color: #007bff;
    text-align: center;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}
p {
    margin: 10px 0;
}
</style>

<?php
require_once 'config/config.php';

echo "<h1><PERSON><PERSON><PERSON><PERSON> Sở <PERSON> - " . SITE_NAME . "</h1>";

try {
    // Kết nối MySQL mà không chọn database
    $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ Kết nối MySQL thành công!</p>";
    
    // Đọc file SQL
    $sql_file = 'database/schema.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("File schema.sql không tồn tại!");
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // Tách các câu lệnh SQL
    $statements = array_filter(array_map('trim', explode(';', $sql_content)));
    
    echo "<p>📄 Đọc file schema.sql thành công!</p>";
    echo "<p>🔄 Đang thực thi " . count($statements) . " câu lệnh SQL...</p>";
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                echo "<p style='color: green; margin-left: 20px;'>✅ " . substr($statement, 0, 50) . "...</p>";
            } catch (PDOException $e) {
                echo "<p style='color: orange; margin-left: 20px;'>⚠️ " . substr($statement, 0, 50) . "... (có thể đã tồn tại)</p>";
            }
        }
    }
    
    echo "<h2>🎉 Thiết Lập Hoàn Tất!</h2>";
    echo "<p><strong>Thông tin đăng nhập Admin mặc định:</strong></p>";
    echo "<ul>";
    echo "<li>Email: <EMAIL></li>";
    echo "<li>Mật khẩu: password</li>";
    echo "</ul>";
    
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Về Trang Chủ</a></p>";
    echo "<p><a href='views/auth/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔑 Đăng Nhập Admin</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Lỗi: " . $e->getMessage() . "</p>";
    echo "<p><strong>Hướng dẫn khắc phục:</strong></p>";
    echo "<ol>";
    echo "<li>Đảm bảo MySQL/XAMPP đang chạy</li>";
    echo "<li>Kiểm tra thông tin kết nối trong config/config.php</li>";
    echo "<li>Tạo database 'web_ban_khoa_hoc' thủ công nếu cần</li>";
    echo "</ol>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background-color: #f8f9fa;
}
h1 {
    color: #007bff;
    text-align: center;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}
p {
    margin: 10px 0;
}
</style>

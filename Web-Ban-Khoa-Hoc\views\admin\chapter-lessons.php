<?php
require_once '../../includes/functions.php';

// Kiểm tra đăng nhập và quyền admin
if (!isLoggedIn() || !isAdmin()) {
    setFlashMessage('error', 'Bạn <PERSON>hông <PERSON>ề<PERSON>ậ<PERSON>rang <PERSON>à<PERSON>');
    redirect(SITE_URL . '/views/auth/login.php');
}

$chapter_id = isset($_GET['chapter_id']) ? (int)$_GET['chapter_id'] : 0;
$course_id = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;

if (!$chapter_id || !$course_id) {
    setFlashMessage('error', 'Chương Hoặc Môn Học <PERSON>hông Tồn Tại');
    redirect('courses.php');
}

global $database;

// Lấy thông tin chương và môn học
$chapter = $database->selectOne(
    "SELECT c.*, co.ten_khoa_hoc 
     FROM course_chapters c 
     JOIN courses co ON c.course_id = co.id 
     WHERE c.id = ? AND c.course_id = ?",
    [$chapter_id, $course_id]
);

if (!$chapter) {
    setFlashMessage('error', 'Chương Không Tồn Tại');
    redirect('courses.php');
}

// Xử lý thêm bài học mới
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_lesson') {
    try {
        if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Token Bảo Mật Không Hợp Lệ');
        }

        $ten_bai_hoc = sanitize($_POST['ten_bai_hoc'] ?? '');
        $noi_dung = $_POST['noi_dung'] ?? '';
        $video_url = sanitize($_POST['video_url'] ?? '');
        $thoi_luong = (int)($_POST['thoi_luong'] ?? 0);
        $mien_phi = isset($_POST['mien_phi']) ? 1 : 0;

        if (empty($ten_bai_hoc)) {
            throw new Exception('Vui Lòng Nhập Tên Bài Học');
        }

        // Tạo slug
        $slug = createSlug($ten_bai_hoc);
        
        // Kiểm tra slug trùng trong cùng khóa học
        $existing = $database->selectOne(
            "SELECT id FROM course_lessons WHERE slug = ? AND course_id = ?", 
            [$slug, $course_id]
        );
        if ($existing) {
            $slug .= '-' . time();
        }

        // Lấy thứ tự tiếp theo
        $max_order = $database->selectOne(
            "SELECT MAX(thu_tu) as max_order FROM course_lessons WHERE chapter_id = ?",
            [$chapter_id]
        )['max_order'] ?? 0;

        // Xử lý upload file tài liệu
        $tai_lieu = null;
        if (isset($_FILES['tai_lieu']) && $_FILES['tai_lieu']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = ROOT_PATH . '/uploads/lessons';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $allowed_types = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
            $tai_lieu = uploadFile($_FILES['tai_lieu'], $upload_dir, $allowed_types);
        }

        $lesson_id = $database->insert(
            "INSERT INTO course_lessons (chapter_id, course_id, ten_bai_hoc, slug, noi_dung, video_url, 
                                        thoi_luong, tai_lieu, thu_tu, mien_phi, created_at) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))",
            [$chapter_id, $course_id, $ten_bai_hoc, $slug, $noi_dung, $video_url, 
             $thoi_luong, $tai_lieu, $max_order + 1, $mien_phi]
        );

        // Cập nhật số bài học trong khóa học
        $database->update(
            "UPDATE courses SET so_bai_hoc = (SELECT COUNT(*) FROM course_lessons WHERE course_id = ?) WHERE id = ?",
            [$course_id, $course_id]
        );

        setFlashMessage('success', 'Thêm Bài Học Thành Công');
        redirect("chapter-lessons.php?chapter_id={$chapter_id}&course_id={$course_id}");

    } catch (Exception $e) {
        $flash_message = ['type' => 'error', 'message' => $e->getMessage()];
    }
}

// Xử lý xóa bài học
if (isset($_GET['action']) && $_GET['action'] === 'delete_lesson' && isset($_GET['lesson_id'])) {
    try {
        $lesson_id = (int)$_GET['lesson_id'];
        
        // Lấy thông tin file để xóa
        $lesson = $database->selectOne("SELECT tai_lieu FROM course_lessons WHERE id = ?", [$lesson_id]);
        
        $database->delete("DELETE FROM course_lessons WHERE id = ? AND chapter_id = ?", [$lesson_id, $chapter_id]);
        
        // Xóa file tài liệu nếu có
        if ($lesson && $lesson['tai_lieu']) {
            $file_path = ROOT_PATH . '/uploads/lessons/' . $lesson['tai_lieu'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
        }

        // Cập nhật số bài học
        $database->update(
            "UPDATE courses SET so_bai_hoc = (SELECT COUNT(*) FROM course_lessons WHERE course_id = ?) WHERE id = ?",
            [$course_id, $course_id]
        );

        setFlashMessage('success', 'Xóa Bài Học Thành Công');
        redirect("chapter-lessons.php?chapter_id={$chapter_id}&course_id={$course_id}");

    } catch (Exception $e) {
        setFlashMessage('error', 'Lỗi: ' . $e->getMessage());
        redirect("chapter-lessons.php?chapter_id={$chapter_id}&course_id={$course_id}");
    }
}

// Lấy danh sách bài học
$lessons = $database->select(
    "SELECT * FROM course_lessons 
     WHERE chapter_id = ? 
     ORDER BY thu_tu ASC",
    [$chapter_id]
);

$flash_message = $flash_message ?? getFlashMessage();
$page_title = 'Quản Lý Bài Học: ' . $chapter['ten_chuong'];

// Nội dung trang
ob_start();
?>

<div class="row">
  <div class="col-12">
    <!-- Breadcrumb -->
    <div class="card mb-4">
      <div class="card-body">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
              <a href="course-edit.php?id=<?= $course_id ?>">
                <?= htmlspecialchars($chapter['ten_khoa_hoc']) ?>
              </a>
            </li>
            <li class="breadcrumb-item">
              <a href="course-chapters.php?course_id=<?= $course_id ?>">Chương</a>
            </li>
            <li class="breadcrumb-item active">
              <?= htmlspecialchars($chapter['ten_chuong']) ?>
            </li>
          </ol>
        </nav>
      </div>
    </div>

    <div class="row">
      <!-- Form thêm bài học -->
      <div class="col-lg-4">
        <div class="card">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="fa fa-plus me-2"></i>Thêm Bài Học Mới
            </h6>
          </div>
          
          <form method="POST" enctype="multipart/form-data">
            <input type="hidden" name="csrf_token" value="<?= generateCSRFToken() ?>">
            <input type="hidden" name="action" value="add_lesson">
            
            <div class="card-body">
              <div class="mb-3">
                <label for="ten_bai_hoc" class="form-label">Tên Bài Học <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="ten_bai_hoc" name="ten_bai_hoc" 
                       placeholder="Nhập tên bài học..." required>
              </div>

              <div class="mb-3">
                <label for="video_url" class="form-label">Link Video YouTube</label>
                <input type="url" class="form-control" id="video_url" name="video_url" 
                       placeholder="https://www.youtube.com/watch?v=...">
                <div class="form-text">Nhập link YouTube để tự động lấy thumbnail và thời lượng</div>
              </div>

              <div class="mb-3">
                <label for="thoi_luong" class="form-label">Thời Lượng (Phút)</label>
                <input type="number" class="form-control" id="thoi_luong" name="thoi_luong" 
                       min="0" placeholder="Ước tính thời gian học">
              </div>

              <div class="mb-3">
                <label for="tai_lieu" class="form-label">Tệp Tài Liệu</label>
                <input type="file" class="form-control" id="tai_lieu" name="tai_lieu" 
                       accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx">
                <div class="form-text">PDF, Word, Excel, PowerPoint (Tối đa 5MB)</div>
              </div>

              <div class="mb-3">
                <label for="noi_dung" class="form-label">Nội Dung Bài Học</label>
                <textarea class="form-control" id="noi_dung" name="noi_dung" rows="6" 
                          placeholder="Nội dung chi tiết bài học..."></textarea>
              </div>

              <div class="mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="mien_phi" name="mien_phi">
                  <label class="form-check-label" for="mien_phi">
                    Bài Học Miễn Phí (Xem Trước)
                  </label>
                </div>
              </div>
            </div>

            <div class="card-footer">
              <button type="submit" class="btn btn-primary w-100">
                <i class="fa fa-plus me-1"></i>Thêm Bài Học
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Danh sách bài học -->
      <div class="col-lg-8">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="card-title mb-0">
              <i class="fa fa-play me-2"></i>Danh Sách Bài Học (<?= count($lessons) ?>)
            </h6>
            <a href="course-chapters.php?course_id=<?= $course_id ?>" class="btn btn-secondary btn-sm">
              <i class="fa fa-arrow-left me-1"></i>Quay Lại Chương
            </a>
          </div>
          
          <div class="card-body">
            <?php if (empty($lessons)): ?>
              <div class="text-center py-5">
                <i class="fa fa-play text-muted" style="font-size: 3rem;"></i>
                <h6 class="mt-3 text-muted">Chưa Có Bài Học Nào</h6>
                <p class="text-muted">Hãy Thêm Bài Học Đầu Tiên Cho Chương Này</p>
              </div>
            <?php else: ?>
              <div id="lessons-list">
                <?php foreach ($lessons as $index => $lesson): ?>
                  <div class="lesson-item border rounded p-3 mb-3" data-lesson-id="<?= $lesson['id'] ?>">
                    <div class="d-flex justify-content-between align-items-start">
                      <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                          <span class="badge bg-success me-2">Bài <?= $index + 1 ?></span>
                          <h6 class="mb-0"><?= htmlspecialchars($lesson['ten_bai_hoc']) ?></h6>
                          <i class="fa fa-grip-vertical ms-2 text-muted" style="cursor: move;" title="Kéo để sắp xếp"></i>
                          
                          <?php if ($lesson['mien_phi']): ?>
                            <span class="badge bg-warning ms-2">Miễn Phí</span>
                          <?php endif; ?>
                        </div>
                        
                        <div class="d-flex align-items-center text-muted small mb-2">
                          <?php if ($lesson['video_url']): ?>
                            <i class="fa fa-video me-1"></i>
                            <span class="me-3">Video</span>
                          <?php endif; ?>
                          
                          <?php if ($lesson['tai_lieu']): ?>
                            <i class="fa fa-file me-1"></i>
                            <span class="me-3">Tài Liệu</span>
                          <?php endif; ?>
                          
                          <?php if ($lesson['thoi_luong']): ?>
                            <i class="fa fa-clock me-1"></i>
                            <span class="me-3"><?= $lesson['thoi_luong'] ?> phút</span>
                          <?php endif; ?>
                          
                          <i class="fa fa-calendar me-1"></i>
                          <span><?= formatDate($lesson['created_at'], 'd/m/Y') ?></span>
                        </div>
                        
                        <?php if ($lesson['noi_dung']): ?>
                          <p class="text-muted mb-0 small">
                            <?= htmlspecialchars(substr($lesson['noi_dung'], 0, 100)) ?>
                            <?= strlen($lesson['noi_dung']) > 100 ? '...' : '' ?>
                          </p>
                        <?php endif; ?>
                      </div>
                      
                      <div class="btn-group" role="group">
                        <?php if ($lesson['video_url']): ?>
                          <a href="<?= htmlspecialchars($lesson['video_url']) ?>" target="_blank" 
                             class="btn btn-sm btn-outline-success" title="Xem Video">
                            <i class="fa fa-play"></i>
                          </a>
                        <?php endif; ?>
                        
                        <?php if ($lesson['tai_lieu']): ?>
                          <a href="<?= UPLOADS_URL ?>/lessons/<?= $lesson['tai_lieu'] ?>" target="_blank" 
                             class="btn btn-sm btn-outline-info" title="Tải Tài Liệu">
                            <i class="fa fa-download"></i>
                          </a>
                        <?php endif; ?>
                        
                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                onclick="editLesson(<?= $lesson['id'] ?>)" title="Chỉnh Sửa">
                          <i class="fa fa-edit"></i>
                        </button>
                        
                        <a href="?chapter_id=<?= $chapter_id ?>&course_id=<?= $course_id ?>&action=delete_lesson&lesson_id=<?= $lesson['id'] ?>" 
                           class="btn btn-sm btn-outline-danger" title="Xóa"
                           onclick="return confirm('Bạn có chắc muốn xóa bài học này?')">
                          <i class="fa fa-trash"></i>
                        </a>
                      </div>
                    </div>
                  </div>
                <?php endforeach; ?>
              </div>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<?php
$content = ob_get_clean();

// JavaScript
$additional_js = '
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
// Drag & Drop sắp xếp bài học
if (document.getElementById("lessons-list")) {
    new Sortable(document.getElementById("lessons-list"), {
        handle: ".fa-grip-vertical",
        animation: 150,
        ghostClass: "sortable-ghost",
        onEnd: function(evt) {
            const lessonIds = Array.from(document.querySelectorAll("[data-lesson-id]"))
                .map(item => item.getAttribute("data-lesson-id"));
            
            fetch("lesson-reorder.php", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    chapter_id: ' . $chapter_id . ',
                    lesson_ids: lessonIds
                })
            });
        }
    });
}

// Tự động lấy thông tin từ YouTube URL
document.getElementById("video_url").addEventListener("blur", function() {
    const url = this.value;
    if (url && url.includes("youtube.com")) {
        // Có thể thêm AJAX call để lấy thông tin video từ YouTube API
        console.log("YouTube URL detected:", url);
    }
});

function editLesson(lessonId) {
    // Chuyển đến trang chỉnh sửa bài học
    window.location.href = "lesson-edit.php?id=" + lessonId + "&chapter_id=' . $chapter_id . '&course_id=' . $course_id . '";
}
</script>
<style>
.lesson-item {
    transition: all 0.3s ease;
}
.lesson-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.sortable-ghost {
    opacity: 0.4;
}
</style>
';

// Include layout
include '../layouts/admin.php';
?>
